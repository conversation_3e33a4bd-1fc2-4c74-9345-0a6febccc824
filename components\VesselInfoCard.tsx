
import React from 'react';
import { VesselReport } from '../types';

interface VesselInfoCardProps {
  report: VesselReport;
  onClick: () => void; // This will now handle setting for modal AND quad view
}

const VesselInfoCard: React.FC<VesselInfoCardProps> = ({ report, onClick }) => {

  const getScoreBadge = () => {
    if (typeof report.malignActivityScore !== 'number') {
      return null;
    }
    
    const score = report.malignActivityScore;
    let badgeClass = 'ds-badge-secondary';
    let label = 'Unknown Risk';

    if (score >= 1 && score <= 39) {
      badgeClass = 'ds-badge-success';
      label = 'Low Risk';
    } else if (score >= 40 && score <= 74) {
      badgeClass = 'ds-badge-warning';
      label = 'Medium Risk';
    } else if (score >= 75 && score <= 100) {
      badgeClass = 'ds-badge-destructive';
      label = 'High Risk';
    }

    return (
      <div 
        className={`ds-badge ${badgeClass}`}
        title={`Malign Activity Score: ${score}/100 - ${label}`}
      >
        Risk Score: {score} <span className="font-normal opacity-80">({label})</span>
      </div>
    );
  };

  const displayTitle = `${report.vesselName || 'Vessel'} - IMO ${report.identifier}`;

  return (
    <div 
      className="ds-panel ds-card-hover flex flex-col cursor-pointer transition-all duration-200"
      onClick={onClick} 
      role="button"
      tabIndex={0}
      onKeyPress={(e) => e.key === 'Enter' && onClick()}
      aria-label={`View details for ${displayTitle}${report.malignActivityScore ? `. Malign activity score: ${report.malignActivityScore}` : ''}${report.malignActivityScoreReason ? `. Reason: ${report.malignActivityScoreReason}` : ''}. Selects for Quad Slide View.`}
    >
      {report.imageUrl ? (
        <img 
          src={report.imageUrl} 
          alt={`Vessel: ${displayTitle}`} 
          className="w-full h-48 object-cover rounded-t-lg" 
        />
      ) : (
        <div className="w-full h-48 flex items-center justify-center rounded-t-lg" style={{backgroundColor: 'var(--ds-border-interactive)'}}>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--ds-text-secondary)'}}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
      )}
      <div className="p-4 flex-grow flex flex-col">
        <h3 className="text-lg font-bold truncate mb-1" title={displayTitle} style={{color: 'var(--ds-primary-color)'}}>
          {displayTitle}
        </h3>
        {report.vesselType && (
           <p className="text-sm mb-1 capitalize" style={{color: 'var(--ds-text-secondary)'}}>{report.vesselType}</p>
        )}
        {report.flag && (
          <p className="text-xs mb-2" style={{color: 'var(--ds-text-primary)'}}>
            <span className="font-semibold" style={{color: 'var(--ds-text-secondary)'}}>Flag:</span> {report.flag}
          </p>
        )}
        {report.malignActivityScore !== undefined && (
          <div className="mb-1">
            {getScoreBadge()}
          </div>
        )}
        {report.malignActivityScoreReason && (
          <p 
            className="text-xs italic mt-0.5 mb-2 truncate"
            style={{color: 'var(--ds-text-secondary)'}}
            title={report.malignActivityScoreReason}
          >
           Reason: {report.malignActivityScoreReason}
          </p>
        )}
        {report.shortSummary && (
          <p className={`text-xs leading-relaxed clamp-3-lines flex-grow ${!report.malignActivityScoreReason ? 'mb-2' : ''}`} title={report.shortSummary} style={{color: 'var(--ds-text-secondary)'}}>
             <span className="font-semibold" style={{color: 'var(--ds-text-primary)'}}>Summary:</span> {report.shortSummary}
          </p>
        )}
         {!report.shortSummary && <div className="flex-grow"></div>} {/* Spacer if no summary */}
      </div>
      <div className="p-3 text-center mt-auto" style={{backgroundColor: 'var(--ds-border-default)'}}>
        <span className="text-sm font-semibold hover:underline" style={{color: 'var(--ds-primary-color)'}}>
          View Full Report & Select for Quad View
        </span>
      </div>
    </div>
  );
};

export default VesselInfoCard;