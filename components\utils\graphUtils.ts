import { CSSProperties } from 'react';
import { GraphNode as CustomGraphNode } from '../types'; // Assuming this is your type for nodes

export const NODE_WIDTH = 170;
export const NODE_HEIGHT = 65; // Adjusted height for icon + single line name/IMO

export const getNodeColor = (nodeType: CustomGraphNode['type'] | undefined): { border: string; background: string; text: string; iconFill: string } => {
  switch (nodeType) {
    case 'vessel':
      return { border: '#22D3EE', background: '#083344', text: '#E0F2FE', iconFill: '#22D3EE' }; 
    case 'person':
      return { border: '#60A5FA', background: '#1E3A8A', text: '#EFF6FF', iconFill: '#60A5FA' }; 
    case 'company':
      return { border: '#34D399', background: '#064E3B', text: '#D1FAE5', iconFill: '#34D399' }; 
    case 'sanction':
      return { border: '#F87171', background: '#7F1D1D', text: '#FEE2E2', iconFill: '#F87171' }; 
    case 'location':
      return { border: '#FACC15', background: '#713F12', text: '#FEF9C3', iconFill: '#FACC15' }; 
    case 'document':
      return { border: '#A78BFA', background: '#4C1D95', text: '#F5F3FF', iconFill: '#A78BFA' }; 
    default: 
      return { border: '#9CA3AF', background: '#374151', text: '#E5E7EB', iconFill: '#9CA3AF' }; 
  }
};

// Helper to get contrasting color for labels on icon backgrounds if needed
export const getContrastingTextColor = (hexcolor: string): string => {
    if (hexcolor.startsWith('#')) {
        hexcolor = hexcolor.slice(1);
    }
    if (hexcolor.length === 3) {
        hexcolor = hexcolor.split('').map(char => char + char).join('');
    }
    const r = parseInt(hexcolor.substring(0, 2), 16);
    const g = parseInt(hexcolor.substring(2, 4), 16);
    const b = parseInt(hexcolor.substring(4, 6), 16);
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
    return (yiq >= 128) ? '#0D1117' : '#E6EDF3';
};