









import { GoogleGenAI, GenerateContentResponse, GroundingChunk, Part } from "@google/genai";
import { GraphData, VesselReport, GraphNode as CustomGraphNode, SingleVesselSearchResult as SingleVesselSearchResultType, SpireVesselData, CrowlingoData } from '../types'; // Renamed to avoid conflict
import { availableTools } from './functionDeclarations';

let aiClientInstance: GoogleGenAI | null = null;

function getAiClient(): GoogleGenAI {
  if (aiClientInstance) {
    return aiClientInstance;
  }

  const apiKeyFromEnv = process.env.API_KEY;

  if (!apiKeyFromEnv) {
    console.error("CRITICAL: API_KEY for Gemini is not set in process.env.API_KEY.");
    throw new Error("AI Service API Key is not configured. Please ensure the API_KEY is available in the environment.");
  }

  try {
    aiClientInstance = new GoogleGenAI({ apiKey: apiKeyFromEnv });
    return aiClientInstance;
  } catch (e) {
    console.error("Failed to initialize GoogleGenAI client:", e);
    throw new Error("Failed to initialize AI Service. The API Key might be invalid or there could be a network issue.");
  }
}

export interface GeminiVesselInfo { // This will be the structure stored in App.tsx state
  reports: VesselReport[]; 
  searchQuery: string; 
  graphData: GraphData | null; 
}

export interface SingleVesselSearchResult extends SingleVesselSearchResultType {}

export interface AISummary {
  analystThoughts: string;
  riskLikelihoodSummary: string;
  otherPointsOfInterest: string;
}


async function generateVesselImage(ai: GoogleGenAI, description: string): Promise<string | undefined> {
  if (!description || description.trim() === 'N/A' || description.trim() === '') {
    console.warn("Image generation skipped: No valid description provided.");
    return undefined;
  }
  try {
    const response = await ai.models.generateImages({
      model: 'imagen-3.0-generate-002',
      prompt: description,
      config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
    });
    if (response.generatedImages && response.generatedImages.length > 0 && response.generatedImages[0].image?.imageBytes) {
      return `data:image/jpeg;base64,${response.generatedImages[0].image.imageBytes}`;
    }
    console.warn("Image generation did not return any images for prompt:", description);
    return undefined;
  } catch (error: any) {
    const errorMessageString = String(error?.message || error?.toString() || '').toUpperCase();
    if (errorMessageString.includes('429') || errorMessageString.includes('RESOURCE_EXHAUSTED')) {
      console.warn(
        `IMAGE GENERATION SKIPPED (API Quota/Rate Limit): Failed to generate image for prompt "${description}". ` +
        `This is likely due to exceeding API usage limits (Error 429). The application will continue without this image. ` +
        `Original error details:`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2)
      );
    } else {
      console.error(`Error generating vessel image for prompt "${description}":`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    }
    return undefined;
  }
}

function extractStructuredDetails(text: string): { details: Partial<VesselReport & { _imagePromptDescription?: string, malignActivityScoreString?: string, latitudeString?: string, longitudeString?: string }>, remainingText: string } {
  const details: Partial<VesselReport & { _imagePromptDescription?: string, malignActivityScoreString?: string, latitudeString?: string, longitudeString?: string }> = {};
  let remainingText = text;

  const patterns = {
    vesselIdentifier: /VESSEL_IDENTIFIER:\s*([^\n]+)/i, // Capture identifier
    vesselName: /VESSEL_NAME:\s*([^\n]+)/i,
    vesselType: /VESSEL_TYPE:\s*([^\n]+)/i,
    flag: /VESSEL_FLAG:\s*([^\n]+)/i,
    shortSummary: /VESSEL_SHORT_SUMMARY:\s*([^\n]+)/i,
    latitudeString: /VESSEL_LAST_KNOWN_LATITUDE:\s*(-?\d{1,3}(?:\.\d+)?|N\/A)/i,
    longitudeString: /VESSEL_LAST_KNOWN_LONGITUDE:\s*(-?\d{1,3}(?:\.\d+)?|N\/A)/i,
    imagePromptDescription: /VESSEL_IMAGE_PROMPT_DESCRIPTION:\s*([\s\S]*?)(?=\n\S+:\s*|$)/i,
    malignActivityScoreString: /VESSEL_MALIGN_ACTIVITY_SCORE:\s*(\d{1,3}|N\/A)/i,
    malignActivityScoreReason: /VESSEL_MALIGN_ACTIVITY_SCORE_REASON:\s*([^\n]+)/i
  };

  let imagePromptDescription: string | undefined = undefined;

  for (const [key, regex] of Object.entries(patterns)) {
    const match = remainingText.match(regex);
    if (match && match[1]) {
      const value = match[1].trim();
      if (key === 'imagePromptDescription') {
        imagePromptDescription = value;
      } else if (key === 'malignActivityScoreString' || key === 'latitudeString' || key === 'longitudeString') {
         (details as any)[key] = value; // Store as string initially
      } else if (key === 'malignActivityScoreReason' || key === 'vesselName' || key === 'vesselType' || key === 'flag' || key === 'shortSummary' || key === 'vesselIdentifier') {
        (details as any)[key] = value !== 'N/A' ? value : undefined;
      }
      else {
        (details as any)[key] = value;
      }
      // Do not remove VESSEL_IDENTIFIER from text as it's part of the structured block
      if (key !== 'vesselIdentifier') {
        remainingText = remainingText.replace(regex, '').trim();
      }
    }
  }
  
  remainingText = remainingText.replace(/^---/, '').trim();
  
  if (details.malignActivityScoreString && details.malignActivityScoreString.toUpperCase() !== 'N/A') {
    const score = parseInt(details.malignActivityScoreString, 10);
    if (!isNaN(score)) {
      details.malignActivityScore = Math.max(1, Math.min(100, score)); // Clamp between 1 and 100
    }
  }
  delete details.malignActivityScoreString; // remove the temporary string field

  if (details.latitudeString && details.latitudeString.toUpperCase() !== 'N/A') {
    const lat = parseFloat(details.latitudeString);
    if (!isNaN(lat)) {
      details.latitude = lat;
    }
  }
  delete details.latitudeString;

  if (details.longitudeString && details.longitudeString.toUpperCase() !== 'N/A') {
    const lon = parseFloat(details.longitudeString);
    if (!isNaN(lon)) {
      details.longitude = lon;
    }
  }
  delete details.longitudeString;


  return { details: {...details, _imagePromptDescription: imagePromptDescription } as any, remainingText };
}


function extractJsonFromText(text: string): { parsedJson: any | null, remainingText: string } {
  const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
  const match = text.match(jsonRegex);
  if (match && match[1]) {
    let remainingText = text.substring(0, match.index) + text.substring(match.index! + match[0].length);
    remainingText = remainingText.trim();
    try {
      const parsedJson = JSON.parse(match[1]);
      return { parsedJson, remainingText };
    } catch (e) {
      console.warn("Attempted to parse JSON from AI response but failed. The JSON block has been removed from the textual report. Error:", e);
      return { parsedJson: null, remainingText }; // Return text with JSON block removed even if parsing fails
    }
  }
  return { parsedJson: null, remainingText: text };
}


// rawIdentifier is expected to be the pure IMO/MMSI number
export async function getSingleVesselOsintReport(rawIdentifier: string): Promise<SingleVesselSearchResult> { 
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';
  
  // Simulate the API call to 'spire-vessel-lookup-production'
  const mockSpireData: SpireVesselData = {
    name: `Vessel ${rawIdentifier}`,
    registered_owner: "Oceanic Traders Inc.",
    type: "Cargo Ship",
    flag: "Panama",
    last_known_latitude: 34.05,
    last_known_longitude: -118.24,
    speed_knots: 15.2,
    course_degrees: 270,
    destination: "Port of Los Angeles",
    eta_timestamp: new Date(Date.now() + 86400000).toISOString()
  };
  const spireDataString = JSON.stringify(mockSpireData, null, 2);

  // Simulate call to 'crowlingo-social-search-production' based on owner from Spire data
  const mockCrowlingoData: CrowlingoData = {
    mentions: [
        {
            source: "X (formerly Twitter)",
            user: "@TradeWinds",
            content: `Chatter about Oceanic Traders Inc. indicates they are expanding their fleet with new cargo ship, the '${mockSpireData.name}'. Analysts watch for impact on pacific trade routes.`,
            timestamp: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 days ago
            sentiment: "neutral"
        },
        {
            source: "Maritime Executive",
            content: "Reports of labor disputes involving Oceanic Traders Inc. surface again. The company has a history of similar issues.",
            timestamp: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 days ago
            sentiment: "negative"
        },
        {
            source: "LinkedIn Post by CEO",
            user: "John Smith (CEO of Oceanic Traders Inc.)",
            content: "Proud to announce our commitment to sustainability with new, more efficient vessels joining our fleet this quarter.",
            timestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
            sentiment: "positive"
        }
    ]
  };
  const crowlingoDataString = JSON.stringify(mockCrowlingoData, null, 2);
  
  const prompt = `Please provide the following key details separately at the BEGINNING of your response, each on a new line, followed by "---" and then the main textual report:
VESSEL_IDENTIFIER: ${rawIdentifier}
VESSEL_NAME: [Official Name of the vessel, if found. If not, state "N/A"]
VESSEL_TYPE: [e.g., Cargo Ship, Oil Tanker, Bulk Carrier. If not, state "N/A"]
VESSEL_FLAG: [Flag State, e.g., Panama. If not, state "N/A"]
VESSEL_SHORT_SUMMARY: [A concise one-sentence description of the vessel, including type, size (if known), and current status or typical operational area. Example: "A 220m bulk carrier built in 2012, currently active in the North Atlantic." If not enough info, state "N/A"]
VESSEL_LAST_KNOWN_LATITUDE: [Latitude of last known position, e.g., 34.0522. If unknown, state "N/A"]
VESSEL_LAST_KNOWN_LONGITUDE: [Longitude of last known position, e.g., -118.2437. If unknown, state "N/A"]
VESSEL_IMAGE_PROMPT_DESCRIPTION: [Provide a detailed description suitable for generating a photorealistic image of this specific vessel. Include its type, typical colors if known for such vessels or its operator, specific features like cranes or container arrangements, and overall appearance. Example: "A large, modern Panamax container ship, predominantly blue hull with a white superstructure, stacked high with multicolored shipping containers, sailing on open, slightly choppy blue water under a clear sky with a few clouds." If not enough info for a good description, state "N/A"]
VESSEL_MALIGN_ACTIVITY_SCORE: [A numerical score from 1 (very low risk) to 100 (very high risk) representing the estimated severity of potential malign activity associated with the vessel, its ownership, and connected entities identified in your investigation. Base this on factors like sanctions, suspicious corporate structures, association with high-risk entities/regions, or other red flags. If no significant red flags are found, assign a low score (e.g., 1-20). If major confirmed sanctions or strong evidence of illicit activity is found, assign a high score (e.g., 80-100). Provide only the number, or "N/A" if assessment is not possible.]
VESSEL_MALIGN_ACTIVITY_SCORE_REASON: [A very brief (max 15 words) explanation for the assigned score. E.g., "Linked to sanctioned entity X." or "No adverse findings." or "Ownership structure involves high-risk jurisdictions." If no score or reason, state "N/A".]
---

--- BEGIN AUTHORITATIVE DATA FROM spire-vessel-lookup-production ---
The following data was retrieved directly from our trusted internal data source and MUST be used as the primary source of truth for the vessel's current state and particulars. Use this data to populate the fields above the '---' separator and to inform your report.

${spireDataString}
--- END AUTHORITATIVE DATA ---

--- BEGIN AUTHORITATIVE DATA FROM crowlingo-social-search-production ---
The following data on social media and web chatter related to the vessel's owner was retrieved. Use this to inform your analysis of public sentiment and recent events.

${crowlingoDataString}
--- END AUTHORITATIVE DATA ---


Perform an in-depth, multi-stage OSINT investigation for the maritime vessel associated with the identifier: ${rawIdentifier}.

The goal is to produce:
1.  A DETAILED TEXTUAL REPORT for this specific vessel (this will follow the "---" separator above).
2.  A NETWORK GRAPH JSON object representing findings related to this vessel.

When conducting your investigation, prioritize information from the following types of sources:
- For corporate registries: OpenCorporates (opencorporates.com), National Company Registries (e.g., UK Companies House, US SEC EDGAR, specific national business registries).
- Beneficial Ownership Databases: Open Ownership Register (register.openownership.org).
- Legal Entity Identifiers: GLEIF (gleif.org) for LEI data.
- Investigative Databases: ICIJ Offshore Leaks Database (offshoreleaks.icij.org), OCCRP Aleph (aleph.occrp.org) for deeper network analysis and potential hidden connections.

TEXTUAL REPORT Structure (for the single vessel ${rawIdentifier}):

Stage 1: Primary Vessel Identification and Sanctions Check.
   a. Identify the vessel's current official name, any known previous names, its type (e.g., tanker, cargo), flag, year built, gross tonnage, and dimensions. (Confirm details with the authoritative data provided above).
   b. Determine its current operational status and last known position (latitude, longitude if available, or general area - confirm with the authoritative data provided above).
   c. Identify this vessel's *registered owner* and its *operator* (if different) through web search.
   d. Using this vessel's current name, previous names, and its owner/operator, perform a search to determine if any of these entities appear on major international sanctions lists (e.g., OFAC, UN, EU, UK HMT). Clearly state the findings.

Stage 2: Detailed Corporate Network Analysis of Registered Owner.
   Utilize the recommended corporate investigation sources mentioned above for this stage.
   a. For the *registered owner* identified in Stage 1c:
      i. Attempt to identify *all current directors* and, if readily available, key past directors. Cross-reference findings from sources like OpenCorporates and national registries.
      ii. For each identified director, investigate and describe:
          - Their relationship to other directors within this same registered owner entity.
          - A list of *other businesses or entities* for which they are currently (or recently) listed as a director or hold a significant registered role. Include the name of these other businesses and the director's role if specified. Utilize OpenCorporates, national registries, and potentially investigative databases like ICIJ/OCCRP for this.
   b. If this vessel's registered owner is part of a larger corporate group or holding structure, briefly describe this relationship if discoverable. Check Open Ownership for beneficial ownership structures.
   c. Based on the provided 'crowlingo-social-search-production' data, analyze and summarize the public sentiment, recent chatter, or any notable news related to the registered owner. Incorporate this sentiment into your overall risk assessment.

Stage 3: Expanded Fleet and Associated Vessel Analysis.
   a. Based on the *registered owner* for THIS vessel (${rawIdentifier}), list other known vessels registered under this owner.
   b. If THIS vessel's *operator* is different, list other vessels operated by this operator.
   c. If *key parent companies, subsidiaries, or affiliated companies* were identified for THIS vessel's owner/operator, list any vessels owned/operated by these affiliated companies.
   d. For each additional vessel found (from 3a, 3b, 3c), state its name, IMO/MMSI, and relationship to this report's primary network. The IMO/MMSI is critical.
   e. If no other vessels are found for these categories, explicitly state this.

NETWORK GRAPH JSON Object:
   AFTER the textual report, provide a SEPARATE JSON object representing the network of entities and relationships discovered FROM THIS SINGLE VESSEL INVESTIGATION (${rawIdentifier}). This JSON should be enclosed in triple backticks (\`\`\`json ... \`\`\`).
   Node Object Structure Example:
   { "id": "unique_node_identifier (e.g., IMO for vessel, unique string for company/person)", "label": "Display_Name", "type": "entity_type", "sourceIdentifiers": ["${rawIdentifier}"] }
   - "sourceIdentifiers" MUST be an array containing ONLY the input identifier for this specific investigation: ["${rawIdentifier}"].
   - For 'vessel' type nodes, the "id" MUST be its IMO or MMSI number. The "label" should be its name.
   Entity types can be: 'vessel', 'person', 'company', 'sanction', 'location', 'document', 'other'. Ensure 'type' is one of these exact strings.
   
   Edge Object Structure Example:
   { "id": "unique_edge_identifier", "source": "id_of_source_node", "target": "id_of_target_node", "label": "relationship_description" }
   Relationship labels can be: 'owned by', 'director of', 'associated with', 'previous name of', 'sanctioned by', 'operated by', 'subsidiary of', 'beneficial owner of', 'affiliated vessel', etc.

   Ensure all significant entities and their direct relationships identified from this vessel's investigation are represented in this JSON structure. All vessels identified in Stage 3 should be included as nodes with appropriate edges.

Include direct links to the primary information sources used where possible within the textual report.
Specify if certain information could not be found from the suggested sources.`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        tools: [{ googleSearch: {} }],
        temperature: 0.1, 
      },
    });

    const fullTextFromAI = response.text || "No information found or the AI could not generate a response.";
    
    // Extract structured details first
    const { details: structuredVesselDetails, remainingText: textAfterDetailsExtraction } = extractStructuredDetails(fullTextFromAI);
    const vesselNameFromDetails = structuredVesselDetails.vesselName !== 'N/A' ? structuredVesselDetails.vesselName : rawIdentifier;
    const imagePromptForImagen = (structuredVesselDetails as any)._imagePromptDescription || 
                                `A photorealistic image of a ${structuredVesselDetails.vesselType || 'generic ship'} named "${vesselNameFromDetails}", sailing at sea.`;
    delete (structuredVesselDetails as any)._imagePromptDescription;


    const { parsedJson: extractedGraphJson, remainingText: textForReport } = extractJsonFromText(textAfterDetailsExtraction);
    
    // Generate image
    const imageUrl = await generateVesselImage(ai, imagePromptForImagen);

    let graphDataFragmentToReturn: GraphData | null = null;
    if (extractedGraphJson && extractedGraphJson.nodes && extractedGraphJson.edges) {
        graphDataFragmentToReturn = {
            nodes: extractedGraphJson.nodes.map((n: any) => {
              let nodeLabel = String(n.label);
              // For vessel nodes, format label as "Name - IMO ID"
              if (n.type === 'vessel' && String(n.id).match(/^\d{7,9}$/)) { // Assuming ID is IMO/MMSI for vessels
                nodeLabel = `${String(n.label || 'Vessel')} - IMO ${String(n.id)}`;
              }
              return { 
                id: String(n.id), // This should be the raw IMO/MMSI for vessels
                label: nodeLabel, 
                type: n.type || 'other', 
                sourceIdentifiers: Array.isArray(n.sourceIdentifiers) && n.sourceIdentifiers.length > 0 ? n.sourceIdentifiers.map(String) : [rawIdentifier],
                data: { label: nodeLabel, type: n.type || 'other' } // Store formatted label here too
              } as CustomGraphNode;
            }),
            edges: extractedGraphJson.edges.map((e: any) => ({ 
              id: String(e.id),
              source: String(e.source), 
              target: String(e.target), 
              label: e.label, 
              type: 'default', 
              animated: true 
            }))
        };
    } else {
        console.warn("Graph data JSON not found or malformed in AI response for " + rawIdentifier);
    }
    
    let sourceLinksForReport: { title: string; uri: string }[] = [];
    if (response.candidates && response.candidates[0]?.groundingMetadata?.groundingChunks) {
        sourceLinksForReport = response.candidates[0].groundingMetadata.groundingChunks
        .map((chunk: GroundingChunk) => ({
          title: chunk.web?.title || chunk.web?.uri || "Source",
          uri: chunk.web?.uri || "#",
        }))
        .filter(link => link.uri !== "#");
        
      const uniqueLinks = new Map<string, { title: string; uri: string }>();
      sourceLinksForReport.forEach(link => {
        if (!uniqueLinks.has(link.uri)) {
          uniqueLinks.set(link.uri, link);
        }
      });
      sourceLinksForReport = Array.from(uniqueLinks.values());
    }

    const report: VesselReport = {
        identifier: rawIdentifier, // Store raw IMO/MMSI
        textResponse: textForReport.trim() || "No textual information provided by AI for this vessel.",
        sourceLinks: sourceLinksForReport,
        flag: structuredVesselDetails.flag !== 'N/A' ? structuredVesselDetails.flag : undefined,
        shortSummary: structuredVesselDetails.shortSummary !== 'N/A' ? structuredVesselDetails.shortSummary : undefined,
        vesselName: vesselNameFromDetails, // Store raw name
        vesselType: structuredVesselDetails.vesselType !== 'N/A' ? structuredVesselDetails.vesselType : undefined,
        latitude: structuredVesselDetails.latitude,
        longitude: structuredVesselDetails.longitude,
        imageUrl: imageUrl,
        malignActivityScore: structuredVesselDetails.malignActivityScore,
        malignActivityScoreReason: structuredVesselDetails.malignActivityScoreReason,
        spireData: mockSpireData,
        crowlingoData: mockCrowlingoData,
    };

    return {
      identifier: rawIdentifier,
      report: report,
      graphDataFragment: graphDataFragmentToReturn,
    };

  } catch (error) {
    console.error(`Error calling Gemini API for identifier ${rawIdentifier}:`, error instanceof Error ? error.toString() : JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    let errorMessage = `Failed to fetch information for ${rawIdentifier} from Gemini AI.`;
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    if (typeof error === 'object' && error !== null && 'message' in error) {
        const lowerCaseMessage = String((error as any).message).toLowerCase();
        if (lowerCaseMessage.includes("api key") || lowerCaseMessage.includes("permission denied")) {
             errorMessage = "The API Key for Gemini is invalid, missing, or lacks permissions. Please check the configuration.";
        } else if (lowerCaseMessage.includes("500") || lowerCaseMessage.includes("unknown")) {
            errorMessage = `Gemini API returned a server error for ${rawIdentifier}. This might be due to prompt complexity or a temporary issue. Details: ${String((error as any).message)}`;
        }
    }
    throw new Error(errorMessage);
  }
}


export async function generateVesselAISummary(vesselReport: VesselReport): Promise<AISummary | null> {
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';

  // Truncate the full text response to manage token limits, if necessary.
  // Focusing on the first part of the report which usually contains key findings.
  const textResponseExcerpt = vesselReport.textResponse.length > 2000 
    ? vesselReport.textResponse.substring(0, 2000) + "..." 
    : vesselReport.textResponse;

  const prompt = `You are an AI Maritime Analyst. Based on the following vessel report data, provide a concise 3-point summary IN JSON FORMAT.
The JSON object MUST have exactly three keys: "analystThoughts", "riskLikelihoodSummary", and "otherPointsOfInterest".
Each value should be a single, concise bullet point string.

Vessel Report Data:
---
Name: ${vesselReport.vesselName || 'N/A'} (IMO: ${vesselReport.identifier})
Type: ${vesselReport.vesselType || 'N/A'}
Flag: ${vesselReport.flag || 'N/A'}
Malign Activity Score: ${vesselReport.malignActivityScore === undefined ? 'N/A' : vesselReport.malignActivityScore}
Malign Activity Score Reason: ${vesselReport.malignActivityScoreReason || 'N/A'}
Short Summary: ${vesselReport.shortSummary || 'N/A'}
Full Textual Report (excerpt for context):
${textResponseExcerpt}
---

Instructions for each key:
- "analystThoughts": Provide a high-level observation or overall assessment of the vessel based on the report.
- "riskLikelihoodSummary": Synthesize insights from the "Malign Activity Score Reason" and the score itself to describe the likelihood or nature of risk.
- "otherPointsOfInterest": Highlight any other single notable finding, detail, or interesting connection from the full report.

Example JSON output structure:
\`\`\`json
{
  "analystThoughts": "This vessel appears to have a complex ownership structure involving multiple jurisdictions.",
  "riskLikelihoodSummary": "High likelihood of risk due to direct links to sanctioned entities and operations in high-risk areas.",
  "otherPointsOfInterest": "The vessel recently changed its flag state, which warrants further monitoring."
}
\`\`\`

Ensure your response is ONLY the JSON object, enclosed in triple backticks with "json" as the language identifier.
Do NOT include any other text before or after the JSON block.
`;

  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        responseMimeType: "application/json", // Request JSON directly
        temperature: 0.2, // Slightly more deterministic for structured output
      },
    });

    let jsonStr = response.text.trim();
    // Remove markdown fence if present
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsedData = JSON.parse(jsonStr) as AISummary;

    // Validate the structure
    if (parsedData && 
        typeof parsedData.analystThoughts === 'string' &&
        typeof parsedData.riskLikelihoodSummary === 'string' &&
        typeof parsedData.otherPointsOfInterest === 'string') {
      return parsedData;
    } else {
      console.warn("AI summary response was not in the expected JSON format:", parsedData);
      throw new Error("AI summary response was not in the expected format.");
    }

  } catch (error) {
    console.error(`Error generating AI summary for vessel ${vesselReport.identifier}:`, error);
    let errorMessage = `Failed to generate AI summary for ${vesselReport.identifier}.`;
     if (error instanceof Error && error.message) {
        errorMessage = error.message;
    }
    // Specific error handling for Gemini can be added here if needed
    throw new Error(errorMessage);
  }
}

// ==========================================================================================
// == Example: Function Calling Implementation
// ==========================================================================================
// The following is an alternative implementation of `getSingleVesselOsintReport` that uses
// Gemini's function calling feature. This allows the model to more intelligently gather
// data by requesting information from the tools defined in `functionDeclarations.ts`.
// This code is provided as a reference and is not currently used by the main application.
// To use it, you would replace the call to `getSingleVesselOsintReport` in App.tsx with
// a call to `getSingleVesselOsintReportWithFunctionCalling`.
// ==========================================================================================

/**
 * Simulates the execution of a declared function. In a real-world application, this
 * function would trigger API calls, database queries, or other external actions.
 * @param name - The name of the function to run.
 * @param args - The arguments for the function, as provided by the Gemini model.
 * @returns The result of the function execution.
 */
async function runFunction(name: string, args: any): Promise<any> {
  console.log(`[geminiService] Simulating function call: ${name} with args:`, args);
  const ai = getAiClient();

  switch (name) {
    case 'spire_vessel_lookup_production':
      // In a real app, this would query the Spire API via a cloud function.
      return {
        name: `Vessel ${args.identifier}`,
        registered_owner: "Oceanic Traders Inc.",
        type: "Cargo Ship",
        flag: "Panama",
        last_known_latitude: 34.05,
        last_known_longitude: -118.24,
        speed_knots: 15.2,
        course_degrees: 270,
        destination: "Port of Los Angeles",
        eta_timestamp: new Date(Date.now() + 86400000).toISOString() // 1 day from now
      };
      
    case 'crowlingo_social_search_production':
        // Simulate social search
        return {
            mentions: [
                {
                    source: "X (formerly Twitter)",
                    content: `Chatter about ${args.entityName} indicates they are expanding their fleet.`,
                    sentiment: "neutral"
                },
                {
                    source: "Maritime News Forum",
                    content: `A post on a forum mentions ${args.entityName} in connection with port delays last month.`,
                    sentiment: "negative"
                }
            ]
        };

    case 'get_corporate_structure':
       // Simulate by making another Gemini call focused on this task.
       const corpPrompt = `Find corporate structure for ${args.companyName} using web search. Return a summary.`;
       const corpResult = await ai.models.generateContent({
           model: 'gemini-2.5-flash',
           contents: corpPrompt,
           config: { tools: [{ googleSearch: {} }] }
       });
       return { summary: corpResult.text };
    
    case 'perform_web_search':
      // This uses the actual googleSearch tool to fulfill the web search request.
      const webResult = await ai.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: args.query,
          config: { tools: [{ googleSearch: {} }] }
      });
      // Extract grounding metadata to return as sources.
      const groundingMetadata = webResult.candidates?.[0]?.groundingMetadata;
      return { 
          summary: webResult.text,
          sources: groundingMetadata?.groundingChunks?.map((chunk: any) => ({ title: chunk.web?.title, uri: chunk.web?.uri })) || [],
      };

    default:
      return { error: `Unknown function: ${name}` };
  }
}


/**
 * An alternative implementation of `getSingleVesselOsintReport` that utilizes the
 * powerful function calling feature of the Gemini API.
 * This function is for demonstration and is not currently wired into the UI.
 */
export async function getSingleVesselOsintReportWithFunctionCalling(rawIdentifier: string): Promise<SingleVesselSearchResult> {
  const ai = getAiClient();
  const model = 'gemini-2.5-flash';
  
  const chat = ai.chats.create({
    model: model,
    config: { tools: availableTools }
  });

  const initialPrompt = `Please conduct a comprehensive OSINT investigation for the maritime vessel with identifier: ${rawIdentifier}.

Your goal is to produce:
1.  A DETAILED TEXTUAL REPORT summarizing all findings, including all required fields like VESSEL_NAME, VESSEL_TYPE, etc.
2.  A NETWORK GRAPH JSON object representing the network of entities and relationships discovered.

Use the available tools to gather information step-by-step. Prioritize using 'spire_vessel_lookup_production' to get primary vessel data. Then, use the owner's name with 'crowlingo_social_search_production' to get public sentiment. Investigate corporate structures with 'get_corporate_structure'. Use 'perform_web_search' for any other OSINT data like sanctions news.

After gathering all necessary information from the tools, synthesize it into the final report and JSON graph. The final output from you should be ONLY the report and the JSON, not another function call. Ensure the final output format is identical to the one requested in the original, non-function-calling prompt.`;

  let finalResponseText = "";
  const allSourceLinks: { title: string; uri: string }[] = [];

  try {
    let response = await chat.sendMessage({ message: initialPrompt });
    
    // Loop to handle the back-and-forth of function calls
    for (let i = 0; i < 10; i++) { // Safety break to prevent infinite loops
      const functionCalls = response.functionCalls;
      if (!functionCalls || functionCalls.length === 0) {
        finalResponseText = response.text;
        break; // No function call, model has responded with final text.
      }

      console.log(`[geminiService] Model wants to call ${functionCalls.length} function(s).`);

      const functionResponses: Part[] = [];

      for(const call of functionCalls) {
        const { name, args } = call;
        const result = await runFunction(name, args);
        
        if (name === 'perform_web_search' && result.sources) {
            allSourceLinks.push(...result.sources);
        }

        functionResponses.push({
            functionResponse: { name, response: result },
        });
      }

      response = await chat.sendMessage({ message: functionResponses });
    }

    if (!finalResponseText && response.text) {
        finalResponseText = response.text;
    }

    if (!finalResponseText) {
       throw new Error("AI model finished its tool use but did not provide a final textual response.");
    }
    
    // ---- Parsing logic is identical to the original function from here ----
    
    const { details: structuredVesselDetails, remainingText: textAfterDetailsExtraction } = extractStructuredDetails(finalResponseText);
    const vesselNameFromDetails = structuredVesselDetails.vesselName !== 'N/A' ? structuredVesselDetails.vesselName : rawIdentifier;
    const imagePromptForImagen = (structuredVesselDetails as any)._imagePromptDescription || `Photorealistic image of a ${structuredVesselDetails.vesselType || 'generic ship'} named "${vesselNameFromDetails}".`;
    delete (structuredVesselDetails as any)._imagePromptDescription;

    const { parsedJson: extractedGraphJson, remainingText: textForReport } = extractJsonFromText(textAfterDetailsExtraction);
    const imageUrl = await generateVesselImage(ai, imagePromptForImagen);

    let graphDataFragmentToReturn: GraphData | null = null;
    if (extractedGraphJson && extractedGraphJson.nodes && extractedGraphJson.edges) {
        graphDataFragmentToReturn = {
            nodes: extractedGraphJson.nodes.map((n: any) => ({
                id: String(n.id),
                label: n.type === 'vessel' ? `${String(n.label || 'Vessel')} - IMO ${String(n.id)}` : String(n.label),
                type: n.type || 'other',
                sourceIdentifiers: [rawIdentifier],
                data: { label: String(n.label), type: n.type || 'other' }
            })),
            edges: extractedGraphJson.edges,
        };
    }
    
    const uniqueLinks = new Map<string, { title: string; uri: string }>();
    allSourceLinks.forEach(link => { if (link && link.uri) uniqueLinks.set(link.uri, link) });
    const sourceLinksForReport = Array.from(uniqueLinks.values());

    const report: VesselReport = {
        identifier: rawIdentifier,
        textResponse: textForReport.trim(),
        sourceLinks: sourceLinksForReport,
        ...structuredVesselDetails
    };

    return {
      identifier: rawIdentifier,
      report: report,
      graphDataFragment: graphDataFragmentToReturn,
    };

  } catch (error) {
    console.error(`Error in function-calling version of getSingleVesselOsintReport for identifier ${rawIdentifier}:`, error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred during the function calling process.";
    throw new Error(errorMessage);
  }
}