<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PROJECT HELMSMAN</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Chakra+Petch:wght@400;600;700&display=swap');

    :root {
      /* Design System Colors - Dark Theme */
      --ds-primary-color: #22D3EE; /* Tealish Cyan */
      --ds-primary-hover-color: #49E0F8;
      --ds-primary-active-color: #70ECFF;
      --ds-primary-text-on-fill: #0D1117;

      --ds-secondary-color: #6B7280; /* Muted Gray */
      --ds-secondary-hover-color: #9CA3AF;
      --ds-secondary-active-color: #BCC1C9;
      --ds-secondary-text-color: #E5E7EB;
      --ds-secondary-bg-color: #374151;
      --ds-secondary-border-color: #4B5563;

      --ds-destructive-color: #F87171; /* Red */
      --ds-destructive-hover-color: #FA8A8A;
      --ds-destructive-active-color: #FC9F9F;
      --ds-destructive-text-on-fill: #FFFFFF;

      --ds-success-color: #6EE7B7; /* Green */
      --ds-success-bg-color: rgba(16, 185, 129, 0.3);
      --ds-warning-color: #FCD34D; /* Yellow/Orange */
      --ds-warning-bg-color: rgba(245, 158, 11, 0.3);

      --ds-background-default: #0D1117;
      --ds-background-component: #161B22;
      --ds-text-primary: #E6EDF3;
      --ds-text-secondary: #9CA3AF;
      --ds-border-default: #374151;
      --ds-border-interactive: #4B5563;
      --ds-focus-ring-color: var(--ds-primary-color);

      /* Icon Button Specific */
      --ds-icon-btn-selected-bg: var(--ds-primary-color);
      --ds-icon-btn-selected-icon: var(--ds-primary-text-on-fill);
      --ds-icon-btn-secondary-bg-hover: #2d333b;
    }

    body {
      background-color: var(--ds-background-default);
      color: var(--ds-text-primary);
      font-family: 'Chakra Petch', sans-serif;
    }

    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: var(--ds-background-component);
      border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: var(--ds-primary-color);
      border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: var(--ds-primary-hover-color);
    }

    /* DS Panel/Card */
    .ds-panel {
      background-color: var(--ds-background-component);
      border: 1px solid var(--ds-border-default);
      border-radius: 0.5rem; /* rounded-lg */
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    }
    .ds-card-hover:hover {
        border-color: var(--ds-primary-color);
        box-shadow: 0 4px 15px rgba(34, 211, 238, 0.2);
    }

    /* DS Input */
    .ds-input {
      background-color: var(--ds-background-component);
      border: 1px solid var(--ds-border-interactive);
      color: var(--ds-text-primary);
      border-radius: 0.375rem;
      padding: 0.625rem 0.75rem;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
    }
    .ds-input:focus {
      outline: none;
      border-color: var(--ds-focus-ring-color);
      box-shadow: 0 0 0 2px var(--ds-focus-ring-color);
    }
    .ds-input::placeholder {
      color: var(--ds-text-secondary);
    }
    .ds-input:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: var(--ds-border-default);
    }

    /* DS Buttons */
    .ds-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      border-radius: 0.375rem;
      padding: 0.5rem 1rem;
      text-transform: uppercase;
      transition: background-color 0.2s, border-color 0.2s, color 0.2s, box-shadow 0.2s;
      cursor: pointer;
      border: 2px solid transparent;
      white-space: nowrap;
    }
    .ds-btn:focus-visible {
      outline: none;
      box-shadow: 0 0 0 3px var(--ds-focus-ring-color);
    }
    .ds-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    .ds-btn-small { padding: 0.375rem 0.75rem; font-size: 0.875rem; }

    /* Filled */
    .ds-btn-filled.ds-btn-primary { background-color: var(--ds-primary-color); color: var(--ds-primary-text-on-fill); }
    .ds-btn-filled.ds-btn-primary:hover:not(:disabled) { background-color: var(--ds-primary-hover-color); }
    .ds-btn-filled.ds-btn-primary:active:not(:disabled) { background-color: var(--ds-primary-active-color); }
    .ds-btn-filled.ds-btn-secondary { background-color: var(--ds-secondary-bg-color); color: var(--ds-secondary-text-color); }
    .ds-btn-filled.ds-btn-secondary:hover:not(:disabled) { background-color: var(--ds-secondary-hover-color); color: var(--ds-primary-text-on-fill); }
    .ds-btn-filled.ds-btn-destructive { background-color: var(--ds-destructive-color); color: var(--ds-destructive-text-on-fill); }
    .ds-btn-filled.ds-btn-destructive:hover:not(:disabled) { background-color: var(--ds-destructive-hover-color); }
    
    /* Outlined */
    .ds-btn-outlined { background-color: transparent; }
    .ds-btn-outlined.ds-btn-primary { border-color: var(--ds-primary-color); color: var(--ds-primary-color); }
    .ds-btn-outlined.ds-btn-primary:hover:not(:disabled), .ds-btn-outlined.ds-btn-primary.ds-btn-selected { background-color: var(--ds-primary-color); color: var(--ds-primary-text-on-fill); }
    .ds-btn-outlined.ds-btn-secondary { border-color: var(--ds-secondary-border-color); color: var(--ds-secondary-text-color); }
    .ds-btn-outlined.ds-btn-secondary:hover:not(:disabled), .ds-btn-outlined.ds-btn-secondary.ds-btn-selected { background-color: var(--ds-secondary-bg-color); color: var(--ds-text-primary); }
    .ds-btn-outlined.ds-btn-destructive { border-color: var(--ds-destructive-color); color: var(--ds-destructive-color); }
    .ds-btn-outlined.ds-btn-destructive:hover:not(:disabled) { background-color: var(--ds-destructive-color); color: var(--ds-destructive-text-on-fill); }
    
    /* Text */
    .ds-btn-text { text-transform: none; }
    .ds-btn-text.ds-btn-primary { color: var(--ds-primary-color); }
    .ds-btn-text.ds-btn-primary:hover:not(:disabled) { color: var(--ds-primary-hover-color); background-color: rgba(34, 211, 238, 0.1); }
    .ds-btn-text.ds-btn-secondary { color: var(--ds-secondary-text-color); }
    .ds-btn-text.ds-btn-secondary:hover:not(:disabled) { color: var(--ds-text-primary); background-color: var(--ds-icon-btn-secondary-bg-hover); }
    
    /* Icon */
    .ds-btn-icon { padding: 0.5rem; border-radius: 9999px; }
    .ds-btn-icon > svg { width: 1.25rem; height: 1.25rem; }
    .ds-btn-icon.ds-btn-primary > svg { fill: var(--ds-primary-color); }
    .ds-btn-icon.ds-btn-primary:hover:not(:disabled) > svg { fill: var(--ds-primary-hover-color); }
    .ds-btn-icon.ds-btn-primary.ds-btn-selected { background-color: var(--ds-icon-btn-selected-bg); }
    .ds-btn-icon.ds-btn-primary.ds-btn-selected > svg { fill: var(--ds-icon-btn-selected-icon); }
    .ds-btn-icon.ds-btn-secondary > svg { fill: var(--ds-secondary-text-color); }
    .ds-btn-icon.ds-btn-secondary:hover:not(:disabled) { background-color: var(--ds-icon-btn-secondary-bg-hover); }
    .ds-btn-icon.ds-btn-secondary:hover:not(:disabled) > svg { fill: var(--ds-text-primary); }
    .ds-btn-icon.ds-btn-destructive > svg { fill: var(--ds-destructive-color); }
    .ds-btn-icon.ds-btn-destructive:hover:not(:disabled) { background-color: rgba(248, 113, 113, 0.1); }

    /* Badge */
    .ds-badge { display: inline-block; padding: 0.25rem 0.625rem; font-size: 0.75rem; font-weight: 600; border-radius: 0.375rem; border: 1px solid transparent; }
    .ds-badge-success { color: var(--ds-success-color); background-color: var(--ds-success-bg-color); border-color: var(--ds-success-color); }
    .ds-badge-warning { color: var(--ds-warning-color); background-color: var(--ds-warning-bg-color); border-color: var(--ds-warning-color); }
    .ds-badge-destructive { color: var(--ds-destructive-hover-color); background-color: rgba(248, 113, 113, 0.3); border-color: var(--ds-destructive-color); }
    .ds-badge-secondary { color: var(--ds-text-secondary); background-color: var(--ds-secondary-bg-color); border-color: var(--ds-secondary-border-color); }

    /* Library Overrides */
    .react-flow__pane { background-color: var(--ds-background-default); }
    .react-flow__attribution { background-color: rgba(13, 17, 23, 0.8) !important; color: var(--ds-text-secondary) !important; }
    .react-flow__controls-button svg { fill: var(--ds-primary-color) !important; }
    .react-flow__controls-button:hover svg { fill: var(--ds-primary-hover-color) !important; }
    .react-flow__edge-text { font-size: 10px !important; fill: var(--ds-text-secondary) !important; }
    
    .prose-dynamic-colors h3, .prose-dynamic-colors h4 { color: var(--ds-primary-color); font-weight: 600; margin-bottom: 0.75rem; margin-top: 1.5rem; padding-bottom: 0.25rem; border-bottom: 1px solid var(--ds-border-default); }
    .prose-dynamic-colors a { color: var(--ds-primary-color); text-decoration: none; }
    .prose-dynamic-colors a:hover { color: var(--ds-primary-hover-color); text-decoration: underline; }
    .prose-dynamic-colors strong { color: var(--ds-text-primary); font-weight: 600; }
    .prose-dynamic-colors, .prose-dynamic-colors p, .prose-dynamic-colors li { color: var(--ds-text-primary); }
    .prose-dynamic-colors ul, .prose-dynamic-colors ol { margin-left: 1.25rem; margin-bottom: 1rem; }
    .prose-dynamic-colors li { margin-bottom: 0.25rem; }

    .esri-popup .esri-popup__main-container { background-color: var(--ds-background-component) !important; color: var(--ds-text-primary) !important; border: 1px solid var(--ds-border-default) !important; box-shadow: 0 5px 15px rgba(0,0,0,0.4); }
    .esri-popup .esri-popup__header-title { color: var(--ds-primary-color) !important; }
    .esri-popup .esri-popup__feature-buttons .esri-popup__button { color: var(--ds-primary-color) !important; }
    .esri-popup .esri-popup__feature-buttons .esri-popup__button:hover { background-color: var(--ds-icon-btn-secondary-bg-hover) !important; }
    .esri-view .esri-view-surface--inset-outline:focus::after { outline: 2px solid var(--ds-focus-ring-color) !important; }
    .esri-widget, .esri-layer-list { background-color: var(--ds-background-component) !important; color: var(--ds-text-primary) !important; border: 1px solid var(--ds-border-default) !important; }
    .esri-layer-list__item:hover { background-color: var(--ds-icon-btn-secondary-bg-hover) !important; }
    .esri-widget__heading { color: var(--ds-primary-color) !important; }
    
    /* Clamp */
    .clamp-3-lines { overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; }

  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai",
    "leaflet": "https://esm.sh/leaflet@^1.9.4",
    "react-leaflet": "https://esm.sh/react-leaflet@^4.2.1",
    "reactflow": "https://esm.sh/reactflow@11.11.4",
    "@dagrejs/dagre": "https://esm.sh/@dagrejs/dagre@^1.1.3",
    "zustand/": "https://esm.sh/zustand@^4.5.4/",
    "use-sync-external-store/": "https://esm.sh/use-sync-external-store@^2.0.0/",
    "react-is": "https://esm.sh/react-is",
    "path": "https://esm.sh/path@^0.12.7",
    "vite": "https://esm.sh/vite@^6.3.5",
    "esri-loader": "https://esm.sh/esri-loader@^3.7.0",
    "url": "https://esm.sh/url@^0.11.4",
    "esri": "https://esm.sh/esri@^0.0.1-security"
  }
}
</script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
crossorigin=""/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reactflow@11.11.4/dist/style.min.css">
<link rel="stylesheet" href="https://js.arcgis.com/4.29/esri/themes/dark/main.css">


<link rel="stylesheet" href="/index.css">
</head>
<body class="custom-scrollbar">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>