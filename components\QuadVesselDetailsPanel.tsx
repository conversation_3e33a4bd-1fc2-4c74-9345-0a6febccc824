
import React from 'react';
import { VesselReport } from '../types';

interface QuadVesselDetailsPanelProps {
  report: VesselReport;
}

const DetailItem: React.FC<{ label: string; value?: string | number | null }> = ({ label, value }) => {
  if (value === undefined || value === null || String(value).trim() === '' || String(value).toUpperCase() === 'N/A') {
    return null;
  }
  return (
    <div className="mb-1.5">
      <span className="font-semibold text-xs uppercase tracking-wider" style={{color: 'var(--ds-text-secondary)'}}>{label}:</span>
      <span className="ml-2 text-sm" style={{color: 'var(--ds-text-primary)'}}>{String(value)}</span>
    </div>
  );
};

const QuadVesselDetailsPanel: React.FC<QuadVesselDetailsPanelProps> = ({ report }) => {
  const displayTitle = `${report.vesselName || 'Vessel'} - IMO ${report.identifier}`;
  
  const getRiskBadge = () => {
    if (typeof report.malignActivityScore !== 'number') {
      return null;
    }
    const score = report.malignActivityScore;
    let badgeClass = 'ds-badge-success';

    if (score >= 75) {
      badgeClass = 'ds-badge-destructive';
    } else if (score >= 40) {
      badgeClass = 'ds-badge-warning';
    }
    
    return (
      <span className={`ds-badge ${badgeClass}`}>
        {score}
      </span>
    );
  };

  return (
    <div className="h-full flex flex-col">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                 {report.imageUrl ? (
                    <img 
                    src={report.imageUrl} 
                    alt={`Vessel: ${displayTitle}`} 
                    className="w-full h-auto max-h-48 object-contain rounded-md border"
                    style={{borderColor: 'var(--ds-border-interactive)'}}
                    />
                ) : (
                    <div className="w-full h-40 flex items-center justify-center rounded-md border" style={{backgroundColor: 'var(--ds-border-interactive)', borderColor: 'var(--ds-border-default)'}}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--ds-text-secondary)'}}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    </div>
                )}
            </div>
            <div>
                <h3 className="text-lg font-bold mb-2 truncate" title={displayTitle} style={{color: 'var(--ds-primary-color)'}}>{displayTitle}</h3>
                <DetailItem label="Type" value={report.vesselType} />
                <DetailItem label="Flag" value={report.flag} />
                <DetailItem label="Lat/Lon" value={report.latitude && report.longitude ? `${report.latitude.toFixed(4)}, ${report.longitude.toFixed(4)}` : 'N/A'} />
                <DetailItem label="Speed" value={report.spireData ? `${report.spireData.speed_knots} knots` : 'N/A'} />
                <DetailItem label="Course" value={report.spireData ? `${report.spireData.course_degrees}°` : 'N/A'} />
                <DetailItem label="Destination" value={report.spireData?.destination} />
                 {report.malignActivityScore !== undefined && (
                     <div className="mt-2">
                        <span className="font-semibold text-xs uppercase tracking-wider" style={{color: 'var(--ds-text-secondary)'}}>Risk Score:</span>
                        <span className="ml-2">{getRiskBadge()}</span>
                        {report.malignActivityScoreReason && <p className="text-xs italic mt-1" style={{color: 'var(--ds-text-secondary)'}}>Reason: {report.malignActivityScoreReason}</p>}
                     </div>
                 )}
            </div>
        </div>
       
    </div>
  );
};

export default QuadVesselDetailsPanel;