

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { VesselReport } from '../types';

interface GeminiVesselInfoDisplayProps {
  report: VesselReport;
}

const DetailItem: React.FC<{ label: string; value?: string | number | React.ReactNode; className?: string }> = ({ label, value, className = '' }) => {
  if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }
  return (
    <div className={`flex flex-col sm:flex-row mb-1 ${className}`}>
      <span className="font-semibold w-full sm:w-32 flex-shrink-0" style={{color: 'var(--ds-text-secondary)'}}>{label}:</span>
      <span className="sm:ml-2 break-words" style={{color: 'var(--ds-text-primary)'}}>{value}</span>
    </div>
  );
};

const formatReportContentToHtml = (text: string): string => {
  if (!text) return '';

  const lines = text.split('\n');
  let html = '';
  let listType: 'ul' | 'ol' | null = null;
  let inParagraph = false;

  const closeCurrentList = () => {
    if (listType === 'ul') html += '</ul>\n';
    if (listType === 'ol') html += '</ol>\n';
    listType = null;
  };

  const closeCurrentParagraph = () => {
    if (inParagraph) html += '</p>\n';
    inParagraph = false;
  };

  const processLineContent = (lineContent: string): string => {
    // 1. Links (ensure class for prose styling)
    lineContent = lineContent.replace(/\[([^\]]+)]\((https?:\/\/[^\s)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    // 2. Bold
    lineContent = lineContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    return lineContent;
  };

  for (const line of lines) {
    if (line.trim() === '') {
      closeCurrentList();
      closeCurrentParagraph();
      continue;
    }

    // Headings (match "## Title" or "### Stage X: Title" or specific keywords)
    if (line.startsWith('### Stage ') || line.startsWith('### ')) {
      closeCurrentList();
      closeCurrentParagraph();
      html += `<h4 class="mt-4 mb-2 text-lg">${processLineContent(line.replace(/###\s*(Stage\s*\d+:\s*)?/, '$1'))}</h4>\n`;
      continue;
    }
    if (line.startsWith('## ') || 
        ["Vessel Particulars", "Last Known Location and Port", "Detailed Corporate Network Analysis of Registered Owner", "Expanded Fleet and Associated Vessel Analysis"].some(h => line.trim().toUpperCase().startsWith(h.toUpperCase()))) {
      closeCurrentList();
      closeCurrentParagraph();
      html += `<h3 class="mt-5 mb-3 text-xl">${processLineContent(line.replace(/^##\s*/, '').trim())}</h3>\n`;
      continue;
    }

    // Lists
    const listItemMatch = line.match(/^\s*[-\*]\s+(.*)/);
    const orderedItemMatch = line.match(/^\s*([a-z0-9]+(?:i)?)\.\s+(.*)/i);

    if (listItemMatch) {
      closeCurrentParagraph();
      if (listType !== 'ul') {
        closeCurrentList();
        html += '<ul class="list-disc pl-5 space-y-1 mb-3">\n';
        listType = 'ul';
      }
      html += `  <li>${processLineContent(listItemMatch[1])}</li>\n`;
      continue;
    }
    
    if (orderedItemMatch) {
      closeCurrentParagraph();
      if (listType !== 'ol') {
        closeCurrentList();
        html += `<ol class="${orderedItemMatch[1].match(/^\d/) ? 'list-decimal' : 'list-[lower-alpha]'} pl-5 space-y-1 mb-3">\n`;
        listType = 'ol';
      }
      html += `  <li>${processLineContent(orderedItemMatch[2])}</li>\n`; // Marker is handled by ol type
      continue;
    }

    closeCurrentList();

    if (!inParagraph) {
      html += '<p class="mb-3 leading-relaxed">\n';
      inParagraph = true;
    }
    html += processLineContent(line) + (inParagraph ? ' ' : '\n');
  }

  closeCurrentList();
  closeCurrentParagraph();
  return html;
};


const GeminiVesselInfoDisplay: React.FC<GeminiVesselInfoDisplayProps> = ({ report }) => {
  const formattedTextResponseHtml = formatReportContentToHtml(report.textResponse);
  
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [speechSynthesisSupported, setSpeechSynthesisSupported] = useState(true);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setSpeechSynthesisSupported(true);
      // Cleanup on component unmount or when report changes
      return () => {
        if (window.speechSynthesis.speaking) {
          window.speechSynthesis.cancel();
        }
        setIsSpeaking(false);
        setIsPaused(false);
      };
    } else {
      setSpeechSynthesisSupported(false);
    }
  }, [report]); // Rerun effect if report changes to reset/cancel speech

  const textToSpeak = useMemo(() => {
    let summary = `Report for ${report.vesselName || 'Vessel'}, IMO ${report.identifier}. `;
    if (report.vesselType && report.vesselType.toLowerCase() !== 'n/a') {
      summary += `Vessel Type: ${report.vesselType}. `;
    }
    if (report.flag && report.flag.toLowerCase() !== 'n/a') {
      summary += `Flag: ${report.flag}. `;
    }
    if (typeof report.malignActivityScore === 'number') {
      summary += `Malign Activity Score: ${report.malignActivityScore}. `;
      if (report.malignActivityScoreReason && report.malignActivityScoreReason.toLowerCase() !== 'n/a') {
        summary += `Reason: ${report.malignActivityScoreReason}. `;
      }
    }
    if (report.shortSummary && report.shortSummary.toLowerCase() !== 'n/a') {
      summary += `Brief Summary: ${report.shortSummary}`;
    } else {
        summary += "No detailed summary available for audio playback."
    }
    return summary.trim();
  }, [report]);

  const handlePlayPause = useCallback(() => {
    if (!speechSynthesisSupported || !textToSpeak) return;

    if (isSpeaking) {
      if (isPaused) {
        window.speechSynthesis.resume();
        setIsPaused(false);
      } else {
        window.speechSynthesis.pause();
        setIsPaused(true);
      }
    } else {
      if (utteranceRef.current && utteranceRef.current.text !== textToSpeak) {
        // If text changed, cancel old one
         window.speechSynthesis.cancel();
      }
      const utterance = new SpeechSynthesisUtterance(textToSpeak);
      utterance.lang = 'en-US';
      utterance.onstart = () => {
        setIsSpeaking(true);
        setIsPaused(false);
      };
      utterance.onend = () => {
        setIsSpeaking(false);
        setIsPaused(false);
        utteranceRef.current = null;
      };
      utterance.onerror = (event: SpeechSynthesisErrorEvent) => {
        console.error(
          `SpeechSynthesisUtterance.onerror - Code: ${event.error}`,
          `Utterance Text (start): "${event.utterance?.text.substring(0, 100)}..."`,
          'Full Event:', event
        );
        setIsSpeaking(false);
        setIsPaused(false);
        setSpeechSynthesisSupported(false); 
        utteranceRef.current = null;
      };
      utteranceRef.current = utterance;
      window.speechSynthesis.speak(utterance);
    }
  }, [isSpeaking, isPaused, textToSpeak, speechSynthesisSupported]);

  const handleStop = useCallback(() => {
    if (!speechSynthesisSupported) return;
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
    setIsPaused(false);
    utteranceRef.current = null;
  }, [speechSynthesisSupported]);

  const handleExportJson = useCallback(() => {
    if (!report) return;

    const jsonString = JSON.stringify(report, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `vessel-report-IMO-${report.identifier}.json`;
    document.body.appendChild(link);
    link.click();
    
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [report]);

  const getRiskBadge = () => {
    if (typeof report.malignActivityScore !== 'number') {
      return <span className="ds-badge ds-badge-secondary">N/A</span>;
    }
    const score = report.malignActivityScore;
    let badgeClass = 'ds-badge-success';
    let label = 'Low Risk';

    if (score >= 75) {
      badgeClass = 'ds-badge-destructive';
      label = 'High Risk';
    } else if (score >= 40) {
      badgeClass = 'ds-badge-warning';
      label = 'Medium Risk';
    }
    
    return (
      <span className={`ds-badge ${badgeClass}`}>
        {score} / 100 ({label})
      </span>
    );
  };
  

  return (
    <div className="p-4 sm:p-6" style={{backgroundColor: 'var(--ds-background-component)', color: 'var(--ds-text-primary)'}}>
      {/* Header Section - Now Flex for Left (Title/Details) and Right (Audio) */}
      <div className="mb-4 pb-4 flex justify-between items-start" style={{borderBottom: '1px solid var(--ds-border-default)'}}>
        {/* Left Side: Title and Details */}
        <div>
          <h2 className="text-2xl font-bold mb-1" style={{color: 'var(--ds-primary-color)'}}>{report.vesselName || `Vessel ${report.identifier}`}</h2>
          <p className="text-sm mb-3" style={{color: 'var(--ds-text-secondary)'}}>IMO: {report.identifier}</p>
          
          <div className="space-y-1 text-sm">
            <DetailItem label="Flag" value={report.flag} />
            <DetailItem label="Type" value={report.vesselType} />
            <DetailItem 
              label="Last Location" 
              value={report.latitude && report.longitude ? `Lat: ${report.latitude.toFixed(4)}, Lon: ${report.longitude.toFixed(4)}` : 'N/A'} 
            />
            <DetailItem label="Risk Assessment" value={getRiskBadge()} />
            {report.malignActivityScoreReason && (
              <DetailItem label="Risk Reason" value={report.malignActivityScoreReason} className="italic"/>
            )}
          </div>
        </div>

        {/* Right Side: Audio Summary Box */}
        <div className="flex-shrink-0 ml-4">
          {speechSynthesisSupported && textToSpeak && (
            <div 
              className="p-2 rounded-md border" 
              style={{
                borderColor: 'var(--ds-border-interactive)', 
                backgroundColor: 'var(--ds-background-component)',
                minWidth: '160px'
              }}
            >
              <div className="flex items-center justify-center space-x-2">
                <button 
                  onClick={handlePlayPause} 
                  className={`ds-btn ds-btn-icon ${isSpeaking && !isPaused ? 'ds-btn-primary ds-btn-selected' : 'ds-btn-secondary'}`}
                  aria-label={isSpeaking && !isPaused ? "Pause summary" : "Play summary"}
                  title={isSpeaking && !isPaused ? "Pause summary" : "Play summary"}
                >
                  {isSpeaking && !isPaused ? (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5">
                      <path d="M5.75 3a.75.75 0 00-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 00.75-.75V3.75A.75.75 0 007.25 3h-1.5zM12.75 3a.75.75 0 00-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 00.75-.75V3.75a.75.75 0 00-.75-.75h-1.5z" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5">
                      <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
                    </svg>
                  )}
                </button>
                <button 
                  onClick={handleStop} 
                  disabled={!isSpeaking}
                  className="ds-btn ds-btn-icon ds-btn-secondary"
                  aria-label="Stop summary"
                  title="Stop summary"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5">
                    <path d="M5.5 5.5A.5.5 0 016 5h8a.5.5 0 01.5.5v8a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5v-8z" />
                  </svg>
                </button>
              </div>
              <p className="text-xs text-center mt-1" style={{color: 'var(--ds-text-secondary)'}}>
                {isSpeaking && !isPaused ? 'Playing...' : isPaused ? 'Paused' : 'Audio Summary'}
              </p>
            </div>
          )}
          {!speechSynthesisSupported && (
            <div className="p-2 rounded-md border" style={{borderColor: 'var(--ds-border-interactive)', backgroundColor: 'var(--ds-background-component)', minWidth: '160px'}}>
              <p className="text-xs text-center py-2.5" style={{color: 'var(--ds-text-secondary)'}}>Audio N/A</p>
            </div>
          )}
        </div>
      </div>

      {/* Summary Box */}
      {report.shortSummary && report.shortSummary.toLowerCase() !== 'n/a' && (
        <div className="mb-6 p-4 rounded-lg shadow" style={{backgroundColor: 'var(--ds-border-interactive)', borderLeft: '4px solid var(--ds-primary-color)'}}>
          <p className="text-sm italic leading-relaxed" style={{color: 'var(--ds-text-primary)'}}>{report.shortSummary}</p>
        </div>
      )}

      {/* Authoritative Data Sections */}
      {report.spireData && (
          <div className="mt-6 mb-6">
              <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--ds-primary-color)', borderBottom: '1px solid var(--ds-border-default)', paddingBottom: '0.5rem'}}>Authoritative Spire AIS Data</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 text-sm">
                  <DetailItem label="Vessel Name" value={report.spireData.name} />
                  <DetailItem label="Registered Owner" value={report.spireData.registered_owner} />
                  <DetailItem label="Vessel Type" value={report.spireData.type} />
                  <DetailItem label="Flag" value={report.spireData.flag} />
                  <DetailItem label="Latitude" value={report.spireData.last_known_latitude.toFixed(4)} />
                  <DetailItem label="Longitude" value={report.spireData.last_known_longitude.toFixed(4)} />
                  <DetailItem label="Speed" value={`${report.spireData.speed_knots} knots`} />
                  <DetailItem label="Course" value={`${report.spireData.course_degrees}°`} />
                  <DetailItem label="Destination" value={report.spireData.destination} />
                  <DetailItem label="ETA" value={new Date(report.spireData.eta_timestamp).toLocaleString()} />
              </div>
          </div>
      )}

      {report.crowlingoData && report.crowlingoData.mentions.length > 0 && (
          <div className="mt-6 mb-6">
              <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--ds-primary-color)', borderBottom: '1px solid var(--ds-border-default)', paddingBottom: '0.5rem'}}>Crowlingo Social Intelligence</h3>
              <ul className="space-y-4">
                  {report.crowlingoData.mentions.map((mention, index) => {
                      const sentimentColor = mention.sentiment === 'positive' ? 'text-green-400' : mention.sentiment === 'negative' ? 'text-red-400' : 'text-yellow-400';
                      return (
                          <li key={index} className="p-3 rounded-lg" style={{backgroundColor: 'var(--ds-border-interactive)'}}>
                              <p className="text-sm mb-1" style={{color: 'var(--ds-text-primary)'}}>"{mention.content}"</p>
                              <div className="flex justify-between items-center text-xs" style={{color: 'var(--ds-text-secondary)'}}>
                                  <span>Source: <strong>{mention.source}</strong> {mention.user && `by ${mention.user}`}</span>
                                  <span className={`font-semibold capitalize ${sentimentColor}`}>{mention.sentiment}</span>
                              </div>
                              <div className="text-xs mt-1" style={{color: 'var(--ds-text-secondary)'}}>{new Date(mention.timestamp).toLocaleString()}</div>
                          </li>
                      );
                  })}
              </ul>
          </div>
      )}

      {/* Main Report Content with CSS variable driven colors */}
      <div
        className="prose-dynamic-colors max-w-none"
        dangerouslySetInnerHTML={{ __html: formattedTextResponseHtml }}
      />

      {/* Footer Section */}
      <div className="mt-8 pt-4" style={{borderTop: '1px solid var(--ds-border-default)'}}>
        {/* Intelligence Sources (if they exist) */}
        {report.sourceLinks && report.sourceLinks.length > 0 && (
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--ds-primary-color)'}}>Intelligence Sources Utilized:</h3>
            <ul className="space-y-2 list-none pl-0">
              {report.sourceLinks.map((link, index) => (
                <li key={index} className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2.5 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" style={{color: 'var(--ds-primary-color)'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <a
                    href={link.uri}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:underline break-all text-sm"
                    style={{color: 'var(--ds-primary-color)'}}
                    onMouseOver={(e) => e.currentTarget.style.color = 'var(--ds-primary-hover-color)'}
                    onMouseOut={(e) => e.currentTarget.style.color = 'var(--ds-primary-color)'}
                    title={link.uri}
                  >
                    {link.title || link.uri}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Export and Disclaimer */}
        <div className="flex justify-between items-center">
            <p className="text-xs italic max-w-lg" style={{color: 'var(--ds-text-secondary)'}}>
            TadaVision AI Report: Information is algorithmically generated via the Gemini API using Google Search. Data accuracy and completeness are not guaranteed. Always cross-verify critical information with authoritative sources.
            </p>
            <button
                onClick={handleExportJson}
                className="ds-btn ds-btn-outlined ds-btn-secondary ds-btn-small flex-shrink-0 ml-4"
                aria-label={`Export vessel report for IMO ${report.identifier} as JSON`}
            >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export JSON
            </button>
        </div>
      </div>
    </div>
  );
};

export default GeminiVesselInfoDisplay;