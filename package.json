{"name": "maritime-info-search", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "latest", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "reactflow": "11.11.4", "@dagrejs/dagre": "^1.1.3", "zustand": "^4.5.4", "use-sync-external-store": "^2.0.0", "react-is": "latest"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}