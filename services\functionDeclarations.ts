import { FunctionDeclaration, Tool, Type } from "@google/genai";

// Note: In a real application, the function implementations would live elsewhere (e.g., calling a cloud function, a database, or another API).
// These declarations simply tell the Gemini model what tools it has at its disposal.

/**
 * Declaration for a function to look up live vessel data from Spire.
 * This represents a call to a trusted, internal system (like a Google Cloud Function wrapping Spire's API).
 */
export const spireVesselLookupProduction: FunctionDeclaration = {
  name: "spire_vessel_lookup_production",
  description: "Looks up real-time and historical vessel locations, along with vessel characteristics from the Spire API endpoint/s. This is the primary source for vessel position, course, speed, and other detailed vessel particulars.",
  parameters: {
    type: Type.OBJECT,
    properties: {
      identifier: {
        type: Type.STRING,
        description: "The vessel's IMO (7 digits) or MMSI (9 digits) number."
      },
    },
    required: ["identifier"],
  },
};

/**
 * Declaration for a function to investigate corporate structures.
 * This could also represent an internal tool that queries specialized APIs.
 */
export const getCorporateStructure: FunctionDeclaration = {
    name: "get_corporate_structure",
    description: "Retrieves corporate ownership, directors, and associated companies for a given entity from corporate registries and investigative databases.",
    parameters: {
      type: Type.OBJECT,
      properties: {
        companyName: {
          type: Type.STRING,
          description: "The legal name of the company to investigate."
        },
      },
      required: ["companyName"],
    },
};

/**
 * Declaration for a function that uses Gemini's web search capability (Google Search Grounding).
 * This represents the "scrape from the web" part of the data collection process.
 */
export const performWebSearch: FunctionDeclaration = {
  name: "perform_web_search",
  description: "Performs a targeted web search using Google Search for information that is not available in internal databases, such as recent news, sanctions lists, or other OSINT details.",
  parameters: {
    type: Type.OBJECT,
    properties: {
      query: {
        type: Type.STRING,
        description: "A highly specific search query. For example: 'OFAC sanctions list for John Doe' or 'news reports on vessel Sea Explorer incident'."
      },
    },
    required: ["query"],
  },
};

/**
 * Declaration for a function to search social media and web chatter.
 * This represents a call to another specialized data service.
 */
export const crowlingoSocialSearchProduction: FunctionDeclaration = {
  name: "crowlingo_social_search_production",
  description: "Searches social media platforms, forums, and the public web for chatter, news, and sentiment related to the provided IMO.",
  parameters: {
    type: Type.OBJECT,
    properties: {
      entityName: {
        type: Type.STRING,
        description: "The vessel's IMO (7 digits) or MMSI (9 digits) number."
      },
    },
    required: ["entityName"],
  },
};


/**
 * The collection of tools to be provided to the Gemini model.
 */
export const availableTools: Tool[] = [
  { functionDeclarations: [spireVesselLookupProduction, getCorporateStructure, performWebSearch, crowlingoSocialSearchProduction] }
];