
import React, { useEffect, useRef, useState } from 'react';
import { loadCss, loadModules } from 'esri-loader';
import { VesselReport } from '../types';

// Declare __esri to avoid TypeScript errors for ArcGIS API types before they are loaded.
declare global {
  namespace __esri {
    // Constructor Interfaces
    interface MapViewConstructor { new(properties?: any): MapView; }
    interface MapConstructor { new(properties?: any): Map; }
    interface GraphicConstructor { new(properties?: any): Graphic; }
    interface GraphicsLayerConstructor { new(properties?: any): GraphicsLayer; }
    interface PointConstructor { new(properties?: any): Point; }
    interface FeatureLayerConstructor { new(properties?: any): FeatureLayer; }
    interface LayerListConstructor { new(properties?: any): LayerList; }

    // Instance Types
    type MapView = any; 
    type Map = any;
    type Graphic = any;
    type GraphicsLayer = any;
    type Point = any;
    type FeatureLayer = any;
    type LayerList = any;
    type Collection<T = any> = { 
        forEach(callback: (item: T) => void): void;
        map<U>(callback: (item: T) => U): Collection<U>;
        toArray(): T[];
        length: number;
        add(item: T): void;
        addMany(items: T[]): void;
        removeAll(): void;
    };
     type ListItem = {
        title: string;
        visible: boolean;
        layer: FeatureLayer | GraphicsLayer; 
     }

    type SimpleMarkerSymbol = {
      type: "simple-marker";
      style?: "circle" | "square" | "cross" | "x" | "diamond" | "triangle" | "path";
      color?: number[] | string;
      size?: string | number;
      outline?: {
        color: number[] | string;
        width: number;
      };
      path?: string;
      angle?: number;
      xoffset?: number | string;
      yoffset?: number | string;
    };

    type PopupTemplate = {
      title?: string | (() => string);
      content?: string | HTMLElement | (() => string | HTMLElement | Promise<string | HTMLElement>) | any[]; 
      outFields?: string[];
      fieldInfos?: any[];
      expressionInfos?: any[];
    };
  }
}


interface ArcGisMapDisplayProps {
  vesselReports: VesselReport[];
  itemVisibilityFilters: Record<string, boolean>;
}

const basemapOptions = [
  { id: 'dark-gray-vector', label: 'Dark Gray Vector' },
  { id: 'gray-vector', label: 'Light Gray Vector' },
  { id: 'streets-vector', label: 'Streets Vector' },
  { id: 'topo-vector', label: 'Topographic Vector' },
  { id: 'satellite', label: 'Satellite Imagery' },
  { id: 'hybrid', label: 'Hybrid Imagery' },
  { id: 'oceans', label: 'Oceans Basemap' },
  { id: 'osm', label: 'OpenStreetMap' },
];

const ArcGisMapDisplay: React.FC<ArcGisMapDisplayProps> = ({ vesselReports, itemVisibilityFilters }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [view, setView] = useState<__esri.MapView | null>(null);
  const [graphicsLayer, setGraphicsLayer] = useState<__esri.GraphicsLayer | null>(null);
  const [modulesLoaded, setModulesLoaded] = useState(false);
  const mapViewRef = useRef<__esri.MapView | null>(null);
  const legendContainerRef = useRef<HTMLDivElement | null>(null);
  const basemapSelectorWidgetRef = useRef<HTMLDivElement | null>(null); 
  const layerListWidgetRef = useRef<__esri.LayerList | null>(null);


  const [selectedBasemap, setSelectedBasemap] = useState<string>('dark-gray-vector');


  const [EsriModules, setEsriModules] = useState<{
    MapView: __esri.MapViewConstructor;
    Map: __esri.MapConstructor;
    GraphicsLayer: __esri.GraphicsLayerConstructor;
    Graphic: __esri.GraphicConstructor;
    Point: __esri.PointConstructor;
    FeatureLayer: __esri.FeatureLayerConstructor;
    LayerList: __esri.LayerListConstructor;
  } | null>(null);

  useEffect(() => {
    loadCss('https://js.arcgis.com/4.29/esri/themes/dark/main.css');

    loadModules([
      'esri/views/MapView',
      'esri/Map',
      'esri/layers/GraphicsLayer',
      'esri/Graphic',
      'esri/geometry/Point',
      'esri/layers/FeatureLayer',
      'esri/widgets/LayerList'
    ])
      .then(([MapView, Map, GraphicsLayer, Graphic, Point, FeatureLayer, LayerList]) => {
        setEsriModules({
          MapView, Map, GraphicsLayer, Graphic, Point, FeatureLayer, LayerList
        });
        setModulesLoaded(true);
        console.log("ArcGIS API modules loaded successfully.");
      })
      .catch(err => {
        console.error("Esri Loader error loading base modules:", err);
      });
  }, []);

  useEffect(() => {
    if (!modulesLoaded || !mapRef.current || !EsriModules || mapViewRef.current) { 
        return;
    }

    console.log("Attempting to initialize ArcGIS map and view...");

    const { MapView: EsriMapView, Map: EsriMap, GraphicsLayer: EsriGraphicsLayer, FeatureLayer: EsriFeatureLayer, LayerList: EsriLayerList } = EsriModules;

    const landingStationsLayer = new EsriFeatureLayer({
        url: "https://services.arcgis.com/bDAhvQYMG4WL8O5o/arcgis/rest/services/Global_Submarine_Cable_Map/FeatureServer/0",
        title: "Landing Station",
        opacity: 0.8,
        listMode: "show",
        visible: true,
        legendEnabled: false,
    });
    
    landingStationsLayer.load()
      .then(() => console.log(`Landing Stations Layer loaded. Title: ${landingStationsLayer.title}, Visible: ${landingStationsLayer.visible}`))
      .catch((error: any) => console.error(`CRITICAL: Error loading Landing Stations Layer. Title: ${landingStationsLayer.title}, URL: ${landingStationsLayer.url}`, error));

    const cablesLayer = new EsriFeatureLayer({
        url: "https://services.arcgis.com/bDAhvQYMG4WL8O5o/arcgis/rest/services/Global_Submarine_Cable_Map/FeatureServer/1",
        title: "Cables",
        opacity: 0.7,
        listMode: "show",
        visible: true,
        legendEnabled: false,
    });
        
    cablesLayer.load()
      .then(() => console.log(`Cables Layer loaded. Title: ${cablesLayer.title}, Visible: ${cablesLayer.visible}`))
      .catch((error: any) => console.error(`CRITICAL: Error loading Cables Layer. Title: ${cablesLayer.title}, URL: ${cablesLayer.url}`, error));


    const map = new EsriMap({
        basemap: selectedBasemap, 
        layers: [landingStationsLayer, cablesLayer] 
    });

    const gLayer = new EsriGraphicsLayer({
        title: "Vessel Locations",
        listMode: "show",
        visible: true
    });
    map.add(gLayer); 
    setGraphicsLayer(gLayer); 

    const localMapViewInstance = new EsriMapView({
      container: mapRef.current as HTMLDivElement,
      map: map,
      center: [0, 20],
      zoom: 3,
      ui: {
        components: ["zoom", "compass", "attribution"]
      },
      popup: {
        dockEnabled: true,
        dockOptions: {
          buttonEnabled: false,
          breakpoint: false,
          position: 'auto'
        },
        collapseEnabled: false,
        highlightEnabled: true,
      }
    });
    
    mapViewRef.current = localMapViewInstance; 
    setView(localMapViewInstance); 

    const layerList = new EsriLayerList({
        view: localMapViewInstance,
        container: document.createElement("div"), // Create a dummy container for now
    });
    localMapViewInstance.ui.add(layerList, "bottom-right");
    layerListWidgetRef.current = layerList;


    const legendDiv = document.createElement('div');
    legendDiv.className = "esri-widget custom-scrollbar"; 
    legendDiv.style.padding = "10px";
    legendDiv.style.backgroundColor = "var(--ds-background-component)";
    legendDiv.style.color = "var(--ds-text-primary)";
    legendDiv.style.border = "1px solid var(--ds-border-default)";
    legendDiv.style.borderRadius = "var(--ds-border-radius, 4px)"; 
    legendDiv.style.maxHeight = "250px"; 
    legendDiv.style.overflowY = "auto";
    legendDiv.style.width = "270px"; 
    legendDiv.style.boxShadow = "0 2px 10px rgba(0,0,0,0.3)";
    legendDiv.style.margin = "10px"; // Added margin
    legendDiv.innerHTML = `<h3 style="color: var(--ds-primary-color); margin-top: 0; margin-bottom: 8px; font-size: 1em; font-weight: 600;">Vessel Legend</h3><ul id="vessel-legend-list" style="list-style: none; padding: 0; margin: 0;"></ul>`;
    
    localMapViewInstance.ui.add(legendDiv, "bottom-left");
    legendContainerRef.current = legendDiv;

    const basemapSelectContainer = document.createElement('div');
    basemapSelectContainer.className = 'esri-widget';
    basemapSelectContainer.style.padding = '10px';
    basemapSelectContainer.style.margin = '10px'; 
    basemapSelectContainer.style.backgroundColor = 'var(--ds-background-component)';
    basemapSelectContainer.style.color = 'var(--ds-text-primary)';
    basemapSelectContainer.style.border = '1px solid var(--ds-border-default)';
    basemapSelectContainer.style.borderRadius = 'var(--ds-border-radius, 4px)';
    basemapSelectContainer.style.boxShadow = '0 2px 10px rgba(0,0,0,0.3)';
    basemapSelectContainer.style.width = '200px';

    const basemapSelectLabel = document.createElement('label');
    basemapSelectLabel.htmlFor = 'basemap-select-dropdown';
    basemapSelectLabel.innerText = 'Basemap:';
    basemapSelectLabel.style.display = 'block';
    basemapSelectLabel.style.fontSize = '0.9em'; // Consistent size
    basemapSelectLabel.style.marginBottom = '6px'; // Spacing
    basemapSelectLabel.style.color = 'var(--ds-text-secondary)';
    basemapSelectLabel.style.fontWeight = '600';

    const basemapSelect = document.createElement('select');
    basemapSelect.id = 'basemap-select-dropdown';
    basemapSelect.className = 'ds-input'; 
    basemapSelect.style.width = '100%';
    basemapSelect.setAttribute('aria-label', 'Select Basemap');

    basemapOptions.forEach(option => {
        const opt = document.createElement('option');
        opt.value = option.id;
        opt.innerText = option.label;
        if (option.id === selectedBasemap) {
            opt.selected = true;
        }
        basemapSelect.appendChild(opt);
    });

    basemapSelect.onchange = (event) => {
        const newBasemapId = (event.target as HTMLSelectElement).value;
        if (mapViewRef.current && mapViewRef.current.map) {
            mapViewRef.current.map.basemap = newBasemapId;
            setSelectedBasemap(newBasemapId); 
        }
    };

    basemapSelectContainer.appendChild(basemapSelectLabel);
    basemapSelectContainer.appendChild(basemapSelect);
    localMapViewInstance.ui.add(basemapSelectContainer, "top-right"); // Changed position
    basemapSelectorWidgetRef.current = basemapSelectContainer;


    localMapViewInstance.when(() => {
        console.log("MapView is ready.");
    });

    return () => {
      if (mapViewRef.current) {
        if (legendContainerRef.current && mapViewRef.current.ui && typeof mapViewRef.current.ui.remove === 'function') {
            try { mapViewRef.current.ui.remove(legendContainerRef.current); } catch(e) {/* ignore */}
            legendContainerRef.current = null;
        }
        if (basemapSelectorWidgetRef.current && mapViewRef.current.ui && typeof mapViewRef.current.ui.remove === 'function') {
            try { mapViewRef.current.ui.remove(basemapSelectorWidgetRef.current); } catch(e) {/* ignore */}
            basemapSelectorWidgetRef.current = null;
        }
         if (layerListWidgetRef.current && mapViewRef.current.ui && typeof mapViewRef.current.ui.remove === 'function') {
            try { mapViewRef.current.ui.remove(layerListWidgetRef.current); } catch(e) {/* ignore */}
            layerListWidgetRef.current = null;
        }
        mapViewRef.current.destroy();
        mapViewRef.current = null;
      }
      setView(null);
      setGraphicsLayer(null);
    };
  }, [modulesLoaded, EsriModules, selectedBasemap]); 


  const getPinColor = (score?: number): number[] => {
    if (typeof score !== 'number') return [107, 114, 128, 0.9]; 
    if (score >= 75) return [248, 113, 113, 0.9]; 
    if (score >= 40) return [250, 204, 21, 0.9];  
    return [34, 197, 94, 0.9];                    
  };

  useEffect(() => {
    if (!view || !graphicsLayer || !vesselReports || !modulesLoaded || !EsriModules || !itemVisibilityFilters) return;

    const { Graphic: EsriGraphic, Point: EsriPoint } = EsriModules;

    graphicsLayer.removeAll();
    const graphicsToAdd: __esri.Graphic[] = [];

    const visiblePlottableVessels = vesselReports.filter(report => 
        itemVisibilityFilters[report.identifier.toUpperCase()] !== false &&
        typeof report.latitude === 'number' && typeof report.longitude === 'number'
    );

    visiblePlottableVessels.forEach(report => {
        const pointGeometry = new EsriPoint({
          longitude: report.longitude,
          latitude: report.latitude
        });

        const pinColor = getPinColor(report.malignActivityScore);

        const simpleMarkerSymbol: __esri.SimpleMarkerSymbol = {
          type: "simple-marker",
          style: "circle",
          color: pinColor,
          size: "10px",
          outline: {
            color: [255, 255, 255, 0.5],
            width: 1
          }
        };

        const attributes = {
          VesselName: report.vesselName || "N/A",
          IMO: report.identifier,
          RiskScoreValue: report.malignActivityScore,
          RiskScoreDisplay: report.malignActivityScore !== undefined ? report.malignActivityScore.toString() : "N/A",
          Flag: report.flag || "N/A",
          Type: report.vesselType || "N/A",
          Latitude: report.latitude!.toFixed(4),
          Longitude: report.longitude!.toFixed(4),
          ShortSummary: report.shortSummary || "No summary available.",
          Speed: report.spireData ? `${report.spireData.speed_knots} knots` : 'N/A',
          Course: report.spireData ? `${report.spireData.course_degrees}°` : 'N/A',
          Destination: report.spireData?.destination || 'N/A',
          ETA: report.spireData ? new Date(report.spireData.eta_timestamp).toLocaleString() : 'N/A'
        };

        const popupTemplate: __esri.PopupTemplate = {
          title: "{VesselName} <span style='font-size: 0.8em; color: var(--ds-text-secondary);'>IMO: {IMO}</span>",
          content: `
            <div style="font-size: 0.9em;">
              <b>Risk Score:</b> {RiskScoreDisplay}<br>
              <b>Flag:</b> {Flag}<br>
              <b>Type:</b> {Type}<br>
              <hr style="margin: 4px 0; border-color: var(--ds-border-default);">
              <b>Coordinates:</b> {Latitude}, {Longitude}<br>
              <b>Speed:</b> {Speed}<br>
              <b>Course:</b> {Course}<br>
              <b>Destination:</b> {Destination}<br>
              <b>ETA:</b> {ETA}<br>
              <hr style="margin: 4px 0; border-color: var(--ds-border-default);">
              <b title="{ShortSummary}">Summary:</b> <span style="display: block; max-height: 60px; overflow-y: auto;" class="custom-scrollbar">{ShortSummary}</span>
            </div>
          `,
          outFields: ["*"]
        };

        const graphic = new EsriGraphic({
          geometry: pointGeometry,
          symbol: simpleMarkerSymbol,
          attributes: attributes,
          popupTemplate: popupTemplate
        });
        graphicsToAdd.push(graphic);
    });
    graphicsLayer.addMany(graphicsToAdd);
    if (graphicsToAdd.length > 0) {
        console.log(`${graphicsToAdd.length} vessel graphics added/updated (visible & plottable: ${visiblePlottableVessels.length}).`);
    } else if (vesselReports.length > 0) {
        console.log(`No vessel graphics to add. All ${vesselReports.length} vessels might be hidden or lack location data.`);
    }

  }, [view, graphicsLayer, vesselReports, modulesLoaded, EsriModules, itemVisibilityFilters]);


  useEffect(() => {
    if (!view || !legendContainerRef.current || !vesselReports || !EsriModules || !itemVisibilityFilters) return;
    const { Point: EsriPoint } = EsriModules;

    const legendListUl = legendContainerRef.current.querySelector('#vessel-legend-list');
    if (!legendListUl) return;

    legendListUl.innerHTML = ''; 

    const reportsForLegend = vesselReports.filter(r =>
        itemVisibilityFilters[r.identifier.toUpperCase()] !== false &&
        typeof r.latitude === 'number' && typeof r.longitude === 'number'
    );
    
    if (reportsForLegend.length === 0) {
        legendListUl.innerHTML = '<li style="font-style: italic; color: var(--ds-text-secondary); font-size: 0.9em; padding: 5px 0;">No visible vessels.</li>';
        return;
    }

    reportsForLegend.forEach(report => {
        const listItem = document.createElement('li');
        listItem.style.display = "flex";
        listItem.style.alignItems = "center";
        listItem.style.padding = "6px 4px";
        listItem.style.cursor = "pointer";
        listItem.style.borderBottom = "1px solid var(--ds-border-interactive)";
        listItem.title = `Click to zoom to ${report.vesselName || report.identifier}`;
        
        listItem.onmouseenter = () => listItem.style.backgroundColor = "var(--ds-icon-btn-secondary-bg-hover)";
        listItem.onmouseleave = () => listItem.style.backgroundColor = "transparent";

        const colorSwatch = document.createElement('span');
        const pinColor = getPinColor(report.malignActivityScore);
        colorSwatch.style.display = "inline-block";
        colorSwatch.style.width = "12px";
        colorSwatch.style.height = "12px";
        colorSwatch.style.borderRadius = "50%";
        colorSwatch.style.backgroundColor = `rgba(${pinColor[0]}, ${pinColor[1]}, ${pinColor[2]}, ${pinColor[3] || 1})`;
        colorSwatch.style.marginRight = "10px";
        colorSwatch.style.flexShrink = "0";

        const textNode = document.createElement('span');
        textNode.textContent = `${report.vesselName || 'Vessel'} - ${report.identifier}`;
        textNode.style.fontSize = "0.8em";
        textNode.style.fontWeight = "500";
        textNode.style.whiteSpace = "nowrap";
        textNode.style.overflow = "hidden";
        textNode.style.textOverflow = "ellipsis";
        textNode.style.color = "var(--ds-text-primary)";


        listItem.appendChild(colorSwatch);
        listItem.appendChild(textNode);

        listItem.onclick = () => {
            if (view && report.longitude != null && report.latitude != null) {
                view.goTo({
                    target: new EsriPoint({ longitude: report.longitude, latitude: report.latitude }),
                    zoom: 14 
                }).catch((error: any) => console.error("Error zooming to vessel:", error));
            }
        };
        legendListUl.appendChild(listItem);
    });
    if (legendListUl.lastChild) {
        (legendListUl.lastChild as HTMLLIElement).style.borderBottom = "none";
    }

  }, [view, vesselReports, itemVisibilityFilters, EsriModules]); // Removed getPinColor from deps as it's stable


  if (!modulesLoaded) {
      return (
        <div className="flex items-center justify-center h-full w-full" style={{backgroundColor: 'var(--ds-background-component)'}}>
            <div className="text-center">
                <svg className="animate-spin h-8 w-8 text-cyan-400 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p style={{color: 'var(--ds-text-secondary)'}}>Loading Map Resources...</p>
            </div>
        </div>
      );
  }

  return <div ref={mapRef} style={{ height: '100%', width: '100%' }} role="application" aria-label="ArcGIS Map View of Vessels and Submarine Cables"></div>;
};

export default ArcGisMapDisplay;