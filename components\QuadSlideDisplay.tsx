


import React, { useMemo } from 'react';
import { VesselReport, GraphData, GraphNode as CustomGraphNodeTypeFromTypes, GraphEdge } from '../types';
import QuadVesselDetailsPanel from './QuadVesselDetailsPanel';
import QuadSocialIntelPanel from './QuadSocialIntelPanel'; // New Import
import NetworkGraphDisplay from './NetworkGraphDisplay';
import ArcGisMapDisplay from './ArcGisMapDisplay';
import { ReactFlowProvider } from 'reactflow';


interface QuadSlideDisplayProps {
  selectedReport: VesselReport; // The specific report for this quad view
  allReports: VesselReport[]; // All fetched reports, potentially for context
  fullGraphData: GraphData | null;
  currentSearchIdentifiers: string[];
  visibilityFilters: Record<string, boolean>;
  onAddVesselToSearch: (identifier: string) => void;
  onRemoveVesselFromSearch: (identifier: string) => void;
  onToggleVesselVisibility: (identifier: string) => void;
  onShowVesselDetails: (identifier: string) => void;
}

const QuadPanel: React.FC<{title: string, children: React.ReactNode, className?: string}> = ({title, children, className=""}) => (
    <div 
        className={`rounded-lg shadow-lg overflow-hidden flex flex-col ${className}`}
        style={{ backgroundColor: 'var(--ds-background-component)', border: '1px solid var(--ds-border-default)' }}
    >
        <h2 
            className="text-md font-semibold p-3" 
            style={{ color: 'var(--ds-primary-color)', borderBottom: '1px solid var(--ds-border-default)', backgroundColor: 'var(--ds-background-component)' }}
        >
            {title}
        </h2>
        <div className="flex-grow p-0 relative custom-scrollbar overflow-y-auto"> 
            {children}
        </div>
    </div>
);


const QuadSlideDisplay: React.FC<QuadSlideDisplayProps> = ({ 
    selectedReport, 
    // allReports, // Not directly used in this component now, but good to have if children need broader context
    fullGraphData,
    currentSearchIdentifiers,
    visibilityFilters,
    onAddVesselToSearch,
    onRemoveVesselFromSearch,
    onToggleVesselVisibility,
    onShowVesselDetails
}) => {

  const focusedGraphData = useMemo(() => {
    if (!fullGraphData || !selectedReport) return null;

    const reportIdentifier = selectedReport.identifier.toUpperCase();
    
    const relevantNodeIds = new Set<string>();
    relevantNodeIds.add(reportIdentifier); 

    const directConnections = new Set<string>();
     fullGraphData.edges.forEach(edge => {
        if (String(edge.source).toUpperCase() === reportIdentifier) {
            directConnections.add(String(edge.target));
        }
        if (String(edge.target).toUpperCase() === reportIdentifier) {
            directConnections.add(String(edge.source));
        }
    });
    directConnections.forEach(id => relevantNodeIds.add(id));
    
    const filteredNodes = fullGraphData.nodes.filter(node => {
        const nodeIdUpper = String(node.id).toUpperCase();
        if (relevantNodeIds.has(nodeIdUpper)) return true;
        // Also include nodes if their sourceIdentifiers array contains the current selectedReport's identifier.
        // This is important if a node (e.g. company) was discovered via this vessel's search.
        return node.sourceIdentifiers?.map(si => si.toUpperCase()).includes(reportIdentifier);
    });

    const finalVisibleNodeIds = new Set(filteredNodes.map(n => String(n.id).toUpperCase()));

    const filteredEdges = fullGraphData.edges.filter(edge => {
        return finalVisibleNodeIds.has(String(edge.source).toUpperCase()) && finalVisibleNodeIds.has(String(edge.target).toUpperCase());
    });
    
    if (filteredNodes.length === 0) return null;

    return { nodes: filteredNodes, edges: filteredEdges };
  }, [fullGraphData, selectedReport]);

  const mapVesselReportArray = useMemo(() => [selectedReport], [selectedReport]);
  const mapVisibilityFilters = useMemo(() => ({ [selectedReport.identifier.toUpperCase()]: true }), [selectedReport.identifier]);


  return (
    <div className="flex-grow grid grid-cols-1 md:grid-cols-2 grid-rows-2 gap-3 p-0.5 h-full max-h-[calc(100vh-270px)]">
      <QuadPanel title="Vessel Snapshot">
        <div className="p-3 h-full"> 
          <QuadVesselDetailsPanel report={selectedReport} />
        </div>
      </QuadPanel>

      <QuadPanel title="Link Analysis">
        {focusedGraphData && focusedGraphData.nodes.length > 0 ? (
           <ReactFlowProvider>
              <NetworkGraphDisplay 
                  graphData={focusedGraphData} 
                  visibilityFilters={visibilityFilters} 
                  currentSearchIdentifiers={currentSearchIdentifiers}
                  onAddVesselToSearch={onAddVesselToSearch}
                  onRemoveVesselFromSearch={onRemoveVesselFromSearch}
                  onToggleVesselVisibility={onToggleVesselVisibility}
                  onShowVesselDetails={onShowVesselDetails}
              />
           </ReactFlowProvider>
        ) : (
          <div className="flex items-center justify-center h-full p-4 text-sm" style={{color: 'var(--ds-text-secondary)'}}>
            No specific link analysis data available for this vessel.
          </div>
        )}
      </QuadPanel>

      <QuadPanel title="Social Intel">
          <QuadSocialIntelPanel report={selectedReport} />
      </QuadPanel>
      
      <QuadPanel title="Geographic Context">
        {(selectedReport.latitude && selectedReport.longitude) ? (
            <ArcGisMapDisplay 
              vesselReports={mapVesselReportArray} 
              itemVisibilityFilters={mapVisibilityFilters}
            />
        ) : (
             <div className="flex items-center justify-center h-full p-4 text-sm" style={{color: 'var(--ds-text-secondary)'}}>
                No location data available for this vessel to display on map.
            </div>
        )}
      </QuadPanel>
    </div>
  );
};

export default QuadSlideDisplay;