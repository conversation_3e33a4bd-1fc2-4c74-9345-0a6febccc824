

import React, { useEffect } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children, title }) => {
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
      document.addEventListener('keydown', handleEscapeKey);
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
      onClick={onClose} // Close on overlay click
    >
      <div 
        className="rounded-lg shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        style={{ backgroundColor: 'var(--ds-background-component)', border: '1px solid var(--ds-border-default)'}}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside modal content
      >
        <header className="flex items-center justify-between p-4 sticky top-0 z-10" style={{borderBottom: '1px solid var(--ds-border-default)', backgroundColor: 'var(--ds-background-component)'}}>
          {title && (
            <h2 id="modal-title" className="text-xl font-semibold" style={{color: 'var(--ds-primary-color)'}}>{title}</h2>
          )}
          {!title && <div />} {/* Placeholder to keep button to the right if no title */}
          <button 
            onClick={onClose}
            className="ds-btn ds-btn-icon ds-btn-secondary"
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </header>
        <div className="overflow-y-auto p-0 flex-grow custom-scrollbar">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;