
import React from 'react';
import <PERSON>actDOM from 'react-dom/client';
import App from './App';

console.log("[index.tsx] Script loaded. Attempting to render React app...");

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error("[index.tsx] Root element with ID 'root' not found in the DOM.");
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
console.log("[index.tsx] React root created. Calling root.render().");
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
console.log("[index.tsx] root.render() called.");