/// <reference path="../types/speech.d.ts" />

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: number;
}

interface ChatInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  error: string | null;
  onRefreshContext: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  isOpen,
  onClose,
  messages,
  onSendMessage,
  isLoading,
  error,
  onRefreshContext,
}) => {
  const [inputText, setInputText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Speech Recognition State
  const [isListening, setIsListening] = useState(false);
  const [speechError, setSpeechError] = useState<string | null>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
      scrollToBottom();
    }
  }, [isOpen]);

  // Initialize Speech Recognition
  useEffect(() => {
    const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognitionAPI) {
      setSpeechError("Speech recognition is not supported by your browser.");
      return;
    }
    
    recognitionRef.current = new SpeechRecognitionAPI();
    const recognition = recognitionRef.current;
    if (!recognition) return; // Should not happen if SpeechRecognitionAPI is defined

    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsListening(true);
      setSpeechError(null);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      let errorMessage = 'An unknown error occurred during speech recognition.';
      if (event.error === 'no-speech') {
        errorMessage = 'No speech was detected. Please try again.';
      } else if (event.error === 'audio-capture') {
        errorMessage = 'Audio capture failed. Ensure your microphone is working.';
      } else if (event.error === 'not-allowed') {
        errorMessage = 'Microphone access denied. Please allow microphone permission in your browser settings.';
      } else if (event.error === 'network') {
        errorMessage = 'Network error during speech recognition.';
      }
      setSpeechError(errorMessage);
      setIsListening(false);
    };

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      const transcript = event.results[0][0].transcript;
      setInputText(transcript);
      // Automatically submit if transcript is not empty, or let user edit
      // For now, just populate the input. User can press send.
    };
    
    return () => {
        if (recognitionRef.current) {
            recognitionRef.current.abort(); // Stop any active recognition
        }
    };

  }, []);

  const toggleListening = () => {
    if (!recognitionRef.current) {
        setSpeechError("Speech recognition not initialized or not supported.");
        return;
    }
    if (isListening) {
      recognitionRef.current.stop();
    } else {
      try {
        recognitionRef.current.start();
      } catch (e: any) {
        setSpeechError(`Could not start listening: ${e.message}`);
        setIsListening(false);
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() && !isLoading) {
      onSendMessage(inputText);
      setInputText('');
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 w-[calc(100%-2rem)] max-w-md h-[70vh] max-h-[500px] rounded-lg shadow-2xl flex flex-col z-50 transition-all duration-300 ease-out transform"
      style={{ backgroundColor: 'var(--ds-background-component)', border: '1px solid var(--ds-border-default)'}}
      role="complementary" 
      aria-label="AI Analyst Chat Panel"
    >
      {/* Header */}
      <header className="flex items-center justify-between p-3 sticky top-0 z-10" style={{borderBottom: '1px solid var(--ds-border-default)', backgroundColor: 'var(--ds-background-component)'}}>
        <h2 className="text-lg font-semibold" style={{color: 'var(--ds-primary-color)'}}>AI Analyst Chat</h2>
        <div className="flex items-center space-x-2">
            <button
                onClick={onRefreshContext}
                disabled={isLoading}
                className="ds-btn ds-btn-icon ds-btn-secondary"
                title="Refresh context and clear chat"
                aria-label="Refresh context and clear chat"
            >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m-15.357-2a8.001 8.001 0 0015.357 2M15 15H9" />
                </svg>
            </button>
            <button 
                onClick={onClose}
                className="ds-btn ds-btn-icon ds-btn-secondary"
                aria-label="Close chat panel"
            >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
      </header>

      {/* Messages Area */}
      <div className="flex-grow p-3 space-y-3 overflow-y-auto custom-scrollbar" aria-live="polite">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div 
              className={`max-w-[80%] p-2.5 rounded-lg shadow ${
                msg.sender === 'user' 
                  ? 'rounded-br-none' 
                  : 'rounded-bl-none'
              }`}
              style={{
                backgroundColor: msg.sender === 'user' ? 'var(--ds-primary-color)' : 'var(--ds-secondary-bg-color)',
                color: msg.sender === 'user' ? 'var(--ds-primary-text-on-fill)' : 'var(--ds-secondary-text-color)'
              }}
            >
              <p className="text-sm whitespace-pre-wrap break-words">{msg.text}</p>
              <p className={`text-xs mt-1 text-right`} style={{color: msg.sender === 'user' ? 'rgba(13, 17, 23, 0.7)' : 'var(--ds-text-secondary)' }}>
                {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}
        {isLoading && messages.length > 0 && messages[messages.length-1].sender === 'user' && (
             <div className="flex justify-start">
                <div className="max-w-[80%] p-2.5 rounded-lg shadow rounded-bl-none" style={{backgroundColor: 'var(--ds-secondary-bg-color)'}}>
                    <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full animate-pulse delay-75" style={{backgroundColor: 'var(--ds-text-secondary)'}}></div>
                        <div className="w-2 h-2 rounded-full animate-pulse delay-150" style={{backgroundColor: 'var(--ds-text-secondary)'}}></div>
                        <div className="w-2 h-2 rounded-full animate-pulse delay-300" style={{backgroundColor: 'var(--ds-text-secondary)'}}></div>
                    </div>
                </div>
            </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Speech Error Display */}
      {speechError && (
        <div className="px-3 pb-1" style={{ backgroundColor: 'var(--ds-background-component)'}}>
          <p className="text-xs text-center" style={{color: 'var(--ds-destructive-color)'}}>{speechError}</p>
        </div>
      )}

      {/* API Error Display */}
      {error && !speechError && ( // Only show API error if no speech error
        <div className="p-2 border-t" style={{borderColor: 'var(--ds-border-default)', backgroundColor: 'rgba(248, 113, 113, 0.1)'}}>
          <p className="text-xs text-center" style={{color: 'var(--ds-destructive-color)'}}>Error: {error}</p>
        </div>
      )}


      {/* Input Area */}
      <form onSubmit={handleSubmit} className="p-3 sticky bottom-0 z-10" style={{borderTop: '1px solid var(--ds-border-default)', backgroundColor: 'var(--ds-background-component)'}}>
        <div className="flex items-center space-x-2">
          {recognitionRef.current && ( // Only show mic button if API is supported
            <button
                type="button"
                onClick={toggleListening}
                className={`ds-btn ds-btn-icon ${isListening ? 'ds-btn-primary ds-btn-selected' : 'ds-btn-secondary'}`}
                style={{ padding: '0.625rem'}}
                disabled={isLoading || !recognitionRef.current}
                aria-label={isListening ? 'Stop listening' : 'Start voice input'}
                title={isListening ? 'Stop listening' : 'Start voice input'}
            >
                {isListening ? (
                     <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5">
                        <path d="M12 18.75a6.75 6.75 0 006.75-6.75V6.75a6.75 6.75 0 00-13.5 0v5.25A6.75 6.75 0 0012 18.75z" />
                        <path d="M6.162 7.02a.75.75 0 011.062 0l1.701 1.702a.75.75 0 11-1.061 1.06l-1.701-1.7V12a.75.75 0 01-1.5 0V9.782l-1.7 1.7A.75.75 0 012.9 10.42l1.7-1.702a.75.75 0 011.062-.519.75.75 0 01.5 1.061v.439zM16.777 7.53a.75.75 0 011.06 0l1.702 1.701a.75.75 0 11-1.06 1.061l-1.702-1.7V12a.75.75 0 11-1.5 0V9.782l-1.701 1.7a.75.75 0 11-1.061-1.06l1.701-1.702a.75.75 0 011.581.519v.44z" />
                     </svg>
                ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5">
                        <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 11-7.5 0V4.5z" />
                        <path d="M6 10.5a.75.75 0 01.75.75v.75a4.5 4.5 0 009 0v-.75a.75.75 0 011.5 0v.75a6 6 0 11-12 0v-.75A.75.75 0 016 10.5zM12 18.75a.75.75 0 00.75-.75V15.75a.75.75 0 00-1.5 0v2.25a.75.75 0 00.75.75z" />
                    </svg>
                )}
            </button>
          )}
          <input
            ref={inputRef}
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Ask about the loaded data..."
            className="ds-input flex-grow"
            disabled={isLoading}
            aria-label="Chat message input"
          />
          <button 
            type="submit" 
            className="ds-btn ds-btn-filled ds-btn-primary"
            style={{ padding: '0.625rem 0.75rem' }}
            disabled={isLoading || !inputText.trim()}
            aria-label="Send chat message"
          >
            {isLoading && messages.length > 0 && messages[messages.length-1].sender === 'user' ? (
                 <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{color: 'var(--ds-primary-text-on-fill)'}}>
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" style={{color: 'var(--ds-primary-text-on-fill)'}}>
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 16.571V11a1 1 0 112 0v5.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                </svg>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;