
import { VesselData, VesselType } from '../types';
import { SHIP_NAMES_PREFIX, SHIP_NAMES_SUFFIX, VESSEL_TYPES_AVAILABLE, GLOBAL_PORT_NAMES, DATA_FETCH_INTERVAL } from '../constants';

const MAX_VESSELS = 75; // Increased for global view
let currentVessels: VesselData[] = [];
let imoCounter = 9000000; // Start for IMO numbers like IMO9000000

function getRandomElement<T,>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

function getRandomNumber(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

function generateRandomVessel(id: string, index: number): VesselData {
  const name = `${getRandomElement(SHIP_NAMES_PREFIX)} ${getRandomElement(SHIP_NAMES_SUFFIX)} ${Math.floor(Math.random() * 1000)}`;
  // Ensure unique IMO: IMO + 7 digits.
  const imoNumber = `IMO${imoCounter + index}`;

  return {
    id,
    name,
    imo: imoNumber,
    latitude: getRandomNumber(-70, 70), // Global latitude range (avoiding extreme poles)
    longitude: getRandomNumber(-180, 180), // Global longitude range
    speed: getRandomNumber(5, 25), // knots
    heading: getRandomNumber(0, 359),
    type: getRandomElement(Object.values(VesselType).filter(v => v !== VesselType.UNKNOWN)),
    destination: getRandomElement(GLOBAL_PORT_NAMES),
    eta: new Date(Date.now() + Math.random() * 1000 * 60 * 60 * 24 * 5).toISOString(), // ETA up to 5 days
    timestamp: Date.now(),
  };
}

function initializeVessels(): void {
  if (currentVessels.length === 0) {
    const newVessels: VesselData[] = [];
    for (let i = 0; i < MAX_VESSELS; i++) {
      newVessels.push(generateRandomVessel(`vessel-${i}`, i));
    }
    currentVessels = newVessels;
  }
}

function updateVesselData(vessel: VesselData): VesselData {
  const speedKnots = vessel.speed;
  const headingRad = (vessel.heading * Math.PI) / 180;
  const earthRadiusKm = 6371;
  
  const distanceKm = (speedKnots * 1.852 * (DATA_FETCH_INTERVAL / 1000)) / 3600;

  const latRad = (vessel.latitude * Math.PI) / 180;
  const lonRad = (vessel.longitude * Math.PI) / 180;

  let newLatRad = Math.asin(
    Math.sin(latRad) * Math.cos(distanceKm / earthRadiusKm) +
    Math.cos(latRad) * Math.sin(distanceKm / earthRadiusKm) * Math.cos(headingRad)
  );
  let newLonRad = lonRad + Math.atan2(
    Math.sin(headingRad) * Math.sin(distanceKm / earthRadiusKm) * Math.cos(latRad),
    Math.cos(distanceKm / earthRadiusKm) - Math.sin(latRad) * Math.sin(newLatRad)
  );

  let newLatitude = (newLatRad * 180) / Math.PI;
  let newLongitude = (newLonRad * 180) / Math.PI;

  // Longitude wrapping
  if (newLongitude > 180) newLongitude -= 360;
  if (newLongitude < -180) newLongitude += 360;

  // Latitude clamping/regeneration at poles (simplified)
  if (newLatitude < -75 || newLatitude > 75) {
    // Find vessel index to pass for consistent IMO generation (if we were to re-generate from scratch)
    // However, for just repositioning, we'll keep its existing IMO and details, just new lat/lon.
    const regeneratedLat = getRandomNumber(-70, 70);
    const regeneratedLon = getRandomNumber(-180, 180);
    return { 
        ...vessel, 
        latitude: regeneratedLat,
        longitude: regeneratedLon,
        destination: getRandomElement(GLOBAL_PORT_NAMES), // Give it a new destination too
        eta: new Date(Date.now() + Math.random() * 1000 * 60 * 60 * 24 * 5).toISOString(),
        timestamp: Date.now() 
    };
  }
  
  const newSpeed = Math.max(0, vessel.speed + getRandomNumber(-0.5, 0.5)); 
  const newHeading = (vessel.heading + getRandomNumber(-2, 2) + 360) % 360; 

  return {
    ...vessel,
    latitude: newLatitude,
    longitude: newLongitude,
    speed: parseFloat(newSpeed.toFixed(1)),
    heading: parseFloat(newHeading.toFixed(0)),
    timestamp: Date.now(),
    destination: Math.random() < 0.005 ? getRandomElement(GLOBAL_PORT_NAMES) : vessel.destination, 
    eta: Math.random() < 0.005 ? new Date(Date.now() + Math.random() * 1000 * 60 * 60 * 24 * 5).toISOString() : vessel.eta,
  };
}


export const fetchVesselData = async (): Promise<VesselData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      initializeVessels(); // Ensures vessels (and their IMOs) are created once
      currentVessels = currentVessels.map(updateVesselData);
      resolve([...currentVessels]);
    }, 500); 
  });
};
