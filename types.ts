
export interface VesselData {
  id: string;
  name: string;
  imo: string; // International Maritime Organization number
  latitude: number;
  longitude: number;
  speed: number; // knots
  heading: number; // degrees from North
  type: VesselType;
  destination: string;
  eta: string; // ISO string or human-readable
  timestamp: number; // epoch ms
}

export enum VesselType {
  CARGO = "Cargo Ship",
  TANKER = "Tanker",
  PASSENGER = "Passenger Ship",
  FISHING = "Fishing Vessel",
  TUG = "Tugboat",
  SAILING = "Sailing Vessel",
  PLEASURE_CRAFT = "Pleasure Craft",
  HIGH_SPEED_CRAFT = "High-Speed Craft",
  UNKNOWN = "Unknown"
}

// Interfaces for Network Graph
export interface GraphNode {
  id: string;
  label: string;
  type: 'vessel' | 'person' | 'company' | 'sanction' | 'location' | 'document' | 'other';
  sourceIdentifiers?: string[]; // Identifiers (e.g., IMOs/MMSIs from search) this node is related to
  // React Flow specific:
  data?: { label: string; type?: string; [key: string]: any }; // Store original label and type here for custom nodes
  position?: { x: number; y: number };
  style?: React.CSSProperties;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  // React Flow specific:
  type?: string; // Type of edge for React Flow (e.g., 'default', 'smoothstep')
  animated?: boolean;
  style?: React.CSSProperties;
  markerEnd?: any; // For arrowheads
}

export interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

export interface SpireVesselData {
    name: string;
    registered_owner: string;
    type: string;
    flag: string;
    last_known_latitude: number;
    last_known_longitude: number;
    speed_knots: number;
    course_degrees: number;
    destination: string;
    eta_timestamp: string;
}

export interface CrowlingoMention {
    source: string;
    user?: string;
    content: string;
    timestamp: string;
    sentiment: "positive" | "negative" | "neutral";
}

export interface CrowlingoData {
    mentions: CrowlingoMention[];
}


// For individual vessel reports
export interface VesselReport {
  identifier: string; // The IMO/MMSI this report is for
  textResponse: string;
  sourceLinks: { title: string; uri: string }[];
  flag?: string; 
  shortSummary?: string; 
  imageUrl?: string; 
  vesselName?: string; // Added to help with card display if identifier is just a number
  vesselType?: string; // Added to help with card display
  malignActivityScore?: number; // Score from 1-100 for malign activity
  malignActivityScoreReason?: string; // Brief reason for the score
  latitude?: number; // Last known latitude
  longitude?: number; // Last known longitude
  spireData?: SpireVesselData;
  crowlingoData?: CrowlingoData;
}

// For the result of a single vessel OSINT search
export interface SingleVesselSearchResult {
  identifier: string; // The IMO/MMSI that was searched
  report: VesselReport;
  graphDataFragment: GraphData | null;
}