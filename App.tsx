

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { getSingleVesselOsintReport, GeminiVesselInfo, SingleVesselSearchResult } from './services/geminiService';
import GeminiVesselInfoDisplay from './components/GeminiVesselInfoDisplay';
import NetworkGraphDisplay from './components/NetworkGraphDisplay';
import VesselInfoCard from './components/VesselInfoCard';
import Modal from './components/Modal';
import { ReactFlowProvider } from 'reactflow';
import { VesselReport, GraphData } from './types';
import ArcGisMapDisplay from './components/ArcGisMapDisplay';
import QuadSlideDisplay from './components/QuadSlideDisplay';
import ChatInterface from './components/ChatInterface'; // New Import
import { GoogleGenAI, Chat } from '@google/genai'; // New Import

type ViewMode = 'report' | 'graph' | 'map' | 'quad';


const extractRawId = (query: string): string => {
  return query.replace(/IMO\s*/i, '').trim().toUpperCase();
};

// Helper function to get AI client (can be moved to a shared utility or service if preferred)
let aiClientInstance: GoogleGenAI | null = null;
function getAiClient(): GoogleGenAI {
  if (aiClientInstance) {
    return aiClientInstance;
  }
  const apiKeyFromEnv = process.env.API_KEY;
  if (!apiKeyFromEnv) {
    console.error("CRITICAL: API_KEY for Gemini is not set in process.env.API_KEY.");
    throw new Error("AI Service API Key is not configured.");
  }
  aiClientInstance = new GoogleGenAI({ apiKey: apiKeyFromEnv });
  return aiClientInstance;
}

// Function to summarize vessel reports for chat context
function summarizeVesselReportsForChat(reports?: VesselReport[]): string {
  if (!reports || reports.length === 0) {
    return "No vessel reports are currently loaded or available in the context.";
  }
  const reportSummaries = reports.map(r => {
    return `Vessel: ${r.vesselName || 'N/A'} (IMO: ${r.identifier})\nType: ${r.vesselType || 'N/A'}\nFlag: ${r.flag || 'N/A'}\nRisk Score: ${r.malignActivityScore === undefined ? 'N/A' : r.malignActivityScore}\nRisk Reason: ${r.malignActivityScoreReason || 'N/A'}\nSummary: ${r.shortSummary || 'N/A'}\nLast Known Location: Lat ${r.latitude === undefined ? 'N/A' : r.latitude.toFixed(4)}, Lon ${r.longitude === undefined ? 'N/A' : r.longitude.toFixed(4)}`;
  }).join("\n\n---\n\n");
  
  return `The following ${reports.length} vessel report(s) are available:\n\n${reportSummaries}`;
}


const App: React.FC = () => {
  console.log("[App.tsx] App component function executing.");

  const [searchQueries, setSearchQueries] = useState<string[]>(['']);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [geminiError, setGeminiError] = useState<string | null>(null);
  const [geminiVesselInfo, setGeminiVesselInfo] = useState<GeminiVesselInfo | null>(null);
  
  const [viewMode, setViewMode] = useState<ViewMode>('report');
  const [itemVisibilityFilters, setItemVisibilityFilters] = useState<Record<string, boolean>>({}); // Renamed from graphVisibilityFilters

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedVesselForModal, setSelectedVesselForModal] = useState<VesselReport | null>(null);
  const [quadViewSelectedVesselIdentifier, setQuadViewSelectedVesselIdentifier] = useState<string | null>(null);


  // Chat State
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
  const [chatMessages, setChatMessages] = useState<Array<{ id: string; sender: 'user' | 'ai'; text: string; timestamp: number }>>([]);
  const [chatSession, setChatSession] = useState<Chat | null>(null);
  const [isChatLoading, setIsChatLoading] = useState<boolean>(false);
  const [chatError, setChatError] = useState<string | null>(null);


  const currentSearchIdentifiers = useMemo(() => { 
    return searchQueries.map(extractRawId).filter(q => q !== '');
  }, [searchQueries]);

  useEffect(() => {
    try {
      const cachedQueries = localStorage.getItem('maritimeOsintApp_searchQueries');
      if (cachedQueries) setSearchQueries(JSON.parse(cachedQueries));
    } catch (error) {
      console.warn("[App.tsx] Failed to load searchQueries from localStorage. Error:", error);
    }

    let parsedInfoFromCache: GeminiVesselInfo | null = null;
    try {
      const cachedInfo = localStorage.getItem('maritimeOsintApp_geminiVesselInfo');
      if (cachedInfo) {
        parsedInfoFromCache = JSON.parse(cachedInfo) as GeminiVesselInfo;
        setGeminiVesselInfo(parsedInfoFromCache);
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to load geminiVesselInfo from localStorage. Error:", error);
    }
    
    try {
      const cachedViewMode = localStorage.getItem('maritimeOsintApp_viewMode');
      if (cachedViewMode) setViewMode(cachedViewMode as ViewMode);
    } catch (error) {
      console.warn("[App.tsx] Failed to load viewMode from localStorage. Error:", error);
    }

    try {
      const cachedQuadSelectedIdentifier = localStorage.getItem('maritimeOsintApp_quadSelectedIdentifier');
      if (cachedQuadSelectedIdentifier) {
        setQuadViewSelectedVesselIdentifier(cachedQuadSelectedIdentifier);
      } else if (parsedInfoFromCache && parsedInfoFromCache.reports.length > 0 && !quadViewSelectedVesselIdentifier) {
        setQuadViewSelectedVesselIdentifier(parsedInfoFromCache.reports[0].identifier);
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to load quadViewSelectedVesselIdentifier from localStorage. Error:", error);
    }


    try {
      const cachedItemFilters = localStorage.getItem('maritimeOsintApp_itemVisibilityFilters'); // Updated key
      if (cachedItemFilters) {
          setItemVisibilityFilters(JSON.parse(cachedItemFilters));
      } else if (parsedInfoFromCache) { 
          const initialFilters: Record<string, boolean> = {};
          if (parsedInfoFromCache?.reports) {
              parsedInfoFromCache.reports.forEach(report => initialFilters[report.identifier.toUpperCase()] = true);
          }
          setItemVisibilityFilters(initialFilters);
      }
    } catch (error) {
        console.warn("[App.tsx] Failed to load or initialize itemVisibilityFilters from localStorage. Error:", error);
    }
  }, []); 

   useEffect(() => {
    if (viewMode === 'quad' && !quadViewSelectedVesselIdentifier && geminiVesselInfo && geminiVesselInfo.reports.length > 0) {
      setQuadViewSelectedVesselIdentifier(geminiVesselInfo.reports[0].identifier);
    }
  }, [viewMode, geminiVesselInfo, quadViewSelectedVesselIdentifier]);


  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_searchQueries', JSON.stringify(searchQueries));
    } catch (error) {
      console.warn("[App.tsx] Failed to save searchQueries to localStorage. Error:", error);
    }
  }, [searchQueries]);

  useEffect(() => {
    try {
      if (geminiVesselInfo) {
        localStorage.setItem('maritimeOsintApp_geminiVesselInfo', JSON.stringify(geminiVesselInfo));
      } else {
        localStorage.removeItem('maritimeOsintApp_geminiVesselInfo');
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to save or remove geminiVesselInfo in localStorage. Error:", error);
    }
  }, [geminiVesselInfo]); 

  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_viewMode', viewMode);
    } catch (error) {
      console.warn("[App.tsx] Failed to save viewMode to localStorage. Error:", error);
    }
  }, [viewMode]);
  
  useEffect(() => {
    try {
      if (quadViewSelectedVesselIdentifier) {
        localStorage.setItem('maritimeOsintApp_quadSelectedIdentifier', quadViewSelectedVesselIdentifier);
      } else {
        localStorage.removeItem('maritimeOsintApp_quadSelectedIdentifier');
      }
    } catch (error) {
      console.warn("[App.tsx] Failed to save quadViewSelectedVesselIdentifier to localStorage. Error:", error);
    }
  }, [quadViewSelectedVesselIdentifier]);



  useEffect(() => {
    try {
      localStorage.setItem('maritimeOsintApp_itemVisibilityFilters', JSON.stringify(itemVisibilityFilters)); // Updated key
    } catch (error) {
      console.warn("[App.tsx] Failed to save itemVisibilityFilters to localStorage. Error:", error);
    }
  }, [itemVisibilityFilters]);

  const openModalWithVessel = (report: VesselReport) => {
    setSelectedVesselForModal(report);
    setIsModalOpen(true);
  };
  
  const handleCardClick = (report: VesselReport) => {
    setQuadViewSelectedVesselIdentifier(report.identifier); 
    openModalWithVessel(report); 
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVesselForModal(null);
  };

  const handleSearchInputChange = (index: number, value: string) => {
    const rawValue = value;
    // Split by comma, semicolon, space, or newline. Filter out empty strings.
    const potentialIdentifiers = rawValue
        .split(/[\s,;\n]+/)
        .map(id => id.trim().replace(/^(IMO|MMSI)\s*/i, '').toUpperCase()) // Remove prefixes and standardize
        .filter(id => /^\d{7,9}$/.test(id)) // Basic validation for 7-9 digits
        .map(id => `IMO ${id}`); // Add back IMO prefix for consistency

    if (potentialIdentifiers.length > 1) {
        const newQueries = [...searchQueries];
        newQueries.splice(index, 1, ...potentialIdentifiers);
        setSearchQueries(newQueries);
    } else {
        const newQueries = [...searchQueries];
        newQueries[index] = rawValue; 
        setSearchQueries(newQueries);
    }
  };


  const addSearchInput = () => {
    setSearchQueries([...searchQueries, '']);
  };
  
  const removeSearchInput = useCallback((identifierToRemoveWithFormat: string, byIndex?: number) => {
    const rawIdToRemove = extractRawId(identifierToRemoveWithFormat);

    if (!rawIdToRemove && typeof byIndex !== 'number') return;

    if (typeof byIndex === 'number') {
        const newQueries = searchQueries.filter((_, i) => i !== byIndex);
        if (newQueries.length === 0) {
            setSearchQueries(['']); 
        } else {
            setSearchQueries(newQueries);
        }
    } else { 
        const newQueries = searchQueries.filter(q => extractRawId(q) !== rawIdToRemove);
         if (newQueries.length === 0) {
            setSearchQueries(['']);
        } else {
            setSearchQueries(newQueries);
        }
    }
    
    if (!rawIdToRemove) return;

    setGeminiVesselInfo(prevInfo => {
      if (!prevInfo) return null;
      const updatedReports = prevInfo.reports.filter(r => r.identifier.toUpperCase() !== rawIdToRemove);
      
      let updatedGraphData: GraphData | null = prevInfo.graphData;
      if (updatedGraphData) {
        const stillVisibleNodes = updatedGraphData.nodes.filter(node => {
            if (!node.sourceIdentifiers || node.sourceIdentifiers.length === 0) return true;
            const remainingSources = node.sourceIdentifiers.filter(sid => sid.toUpperCase() !== rawIdToRemove);
            if (remainingSources.length > 0) {
                node.sourceIdentifiers = remainingSources;
                return true;
            }
            return false;
        });
        const visibleNodeIds = new Set(stillVisibleNodes.map(n => n.id));
        const stillVisibleEdges = updatedGraphData.edges.filter(edge => 
            visibleNodeIds.has(String(edge.source)) && visibleNodeIds.has(String(edge.target))
        );
         updatedGraphData = { nodes: stillVisibleNodes, edges: stillVisibleEdges };
      }

      if (updatedReports.length === 0 && (!updatedGraphData || updatedGraphData.nodes.length === 0)) return null;
      return { ...prevInfo, reports: updatedReports, graphData: updatedGraphData, searchQuery: updatedReports.map(r => r.identifier).join(', ') };
    });

    setItemVisibilityFilters(prev => { // Updated state setter
      const updated = { ...prev };
      delete updated[rawIdToRemove]; 
      return updated;
    });
    setLoadingStates(prev => {
        const updated = {...prev};
        Object.keys(updated).forEach(key => {
            if (extractRawId(key) === rawIdToRemove) {
                delete updated[key];
            }
        });
        return updated;
    });

    if (quadViewSelectedVesselIdentifier && quadViewSelectedVesselIdentifier.toUpperCase() === rawIdToRemove) {
        setQuadViewSelectedVesselIdentifier(geminiVesselInfo && geminiVesselInfo.reports.length > 1 ? geminiVesselInfo.reports.find(r => r.identifier.toUpperCase() !== rawIdToRemove)?.identifier || null : null);
    }


  }, [searchQueries, quadViewSelectedVesselIdentifier, geminiVesselInfo]);


  const handleIndividualSearch = async (identifierToSearch: string) => {
    const trimmedFullIdentifier = identifierToSearch.trim();
    if (!trimmedFullIdentifier) {
      setGeminiError(`Input is empty. Please enter an IMO or MMSI.`);
      return; // Return instead of throw for Promise.all handling
    }
    const rawIdentifier = extractRawId(trimmedFullIdentifier);
     if (!rawIdentifier.match(/^\d{7,9}$/)) { 
      setGeminiError(`Invalid format for "${trimmedFullIdentifier}". Please use IMO (7 digits) or MMSI (9 digits), optionally prefixed with "IMO".`);
      return; // Return instead of throw
    }
    
    setLoadingStates(prev => ({ ...prev, [trimmedFullIdentifier]: true })); 
    setGeminiError(null); 

    try {
      const result: SingleVesselSearchResult = await getSingleVesselOsintReport(rawIdentifier);
      
      setGeminiVesselInfo(prevInfo => {
        const existingReports = prevInfo ? prevInfo.reports : [];
        const existingGraphData = prevInfo ? prevInfo.graphData : null;

        const reportIndex = existingReports.findIndex(r => r.identifier.toUpperCase() === result.identifier.toUpperCase());
        let updatedReports: VesselReport[];
        if (reportIndex > -1) {
          updatedReports = [...existingReports];
          updatedReports[reportIndex] = result.report;
        } else {
          updatedReports = [...existingReports, result.report];
        }

        let mergedGraphData: GraphData | null = existingGraphData;
        if (result.graphDataFragment) {
          if (!mergedGraphData || mergedGraphData.nodes.length === 0) {
            mergedGraphData = result.graphDataFragment;
          } else {
            const newNodesMap = new Map(mergedGraphData.nodes.map(n => [n.id, n]));
            result.graphDataFragment.nodes.forEach(fragNode => {
              if (newNodesMap.has(fragNode.id)) {
                const existingNode = newNodesMap.get(fragNode.id)!;
                const combinedSourceIds = new Set([...(existingNode.sourceIdentifiers || []), ...(fragNode.sourceIdentifiers || [])]);
                existingNode.sourceIdentifiers = Array.from(combinedSourceIds);
                existingNode.label = fragNode.label; 
                existingNode.type = fragNode.type;
                if (existingNode.data) {
                    existingNode.data.label = fragNode.label;
                    existingNode.data.type = fragNode.type;
                } else { 
                    existingNode.data = { label: fragNode.label, type: fragNode.type };
                }
              } else {
                newNodesMap.set(fragNode.id, fragNode);
              }
            });
            mergedGraphData.nodes = Array.from(newNodesMap.values());

            const newEdgesMap = new Map(mergedGraphData.edges.map(e => [e.id, e]));
            result.graphDataFragment.edges.forEach(fragEdge => {
              if (!newEdgesMap.has(fragEdge.id)) {
                newEdgesMap.set(fragEdge.id, fragEdge);
              }
            });
            mergedGraphData.edges = Array.from(newEdgesMap.values());
          }
        }
        
        const newSearchQueryString = updatedReports.map(r => r.identifier).join(', ');
        return { reports: updatedReports, searchQuery: newSearchQueryString, graphData: mergedGraphData };
      });
      
      if ((!quadViewSelectedVesselIdentifier && geminiVesselInfo?.reports.length === 0) || (viewMode === 'quad' && !quadViewSelectedVesselIdentifier)) {
        setQuadViewSelectedVesselIdentifier(result.report.identifier);
      }


      setItemVisibilityFilters(prev => ({ ...prev, [rawIdentifier.toUpperCase()]: true })); // Ensure visibility on new search

    } catch (error: any) {
      console.error(`Error in handleIndividualSearch for ${trimmedFullIdentifier}:`, error);
      setGeminiError(error.message || `An unknown error occurred while searching for ${trimmedFullIdentifier}.`);
      // Do not re-throw here if Promise.all is used, as it would stop other searches.
      // The error is already set for the UI.
    } finally {
      setLoadingStates(prev => ({ ...prev, [trimmedFullIdentifier]: false }));
    }
  };


  const handleSearchAll = async () => {
    setGeminiError(null);
    const searchesToInitiate: string[] = [];
    for (const query of searchQueries) {
        const trimmedQuery = query.trim();
        const rawId = extractRawId(trimmedQuery);
        if (trimmedQuery && rawId.match(/^\d{7,9}$/) && !loadingStates[trimmedQuery]) {
            searchesToInitiate.push(trimmedQuery);
        }
    }

    if (searchesToInitiate.length === 0) {
        setGeminiError("No valid, new queries to search. All inputs are empty, invalid, or already processing/processed.");
        return;
    }
    
    setLoadingStates(prev => {
        const newLoadingStates = {...prev};
        searchesToInitiate.forEach(q => newLoadingStates[q] = true);
        return newLoadingStates;
    });

    const searchPromises = searchesToInitiate.map(query => 
        handleIndividualSearch(query).catch(error => {
            // This catch is for unexpected errors not handled within handleIndividualSearch
            // or if handleIndividualSearch itself re-throws (which it shouldn't for API errors).
            console.error(`Unhandled error during "Search All" promise for query "${query}":`, error);
            // Individual errors are already set by handleIndividualSearch, so this is mostly for logging.
        })
    );

    try {
        await Promise.all(searchPromises);
        // All searches have completed (either successfully or with individual errors handled).
        console.log("All parallel searches initiated by 'Search All' have completed processing.");
    } catch (error) {
        // This catch block will only be hit if Promise.all itself fails,
        // which shouldn't happen if individual promises have their own .catch.
        // However, it's good practice for safety.
        console.error("Critical error during Promise.all in handleSearchAll:", error);
        setGeminiError("A critical error occurred while processing multiple searches. Please check console.");
    }
    // Individual loading states are managed within handleIndividualSearch.
    // No global "all searches done" loading state change is strictly needed here
    // as UI reflects individual query statuses.
  };


  const clearGeminiSearch = () => {
    setGeminiVesselInfo(null);
    setGeminiError(null);
    setItemVisibilityFilters({}); // Reset item visibility
    setLoadingStates({});
    setQuadViewSelectedVesselIdentifier(null);
  };

  const toggleItemVisibility = (rawIdentifier: string) => { // Renamed from toggleGraphFilter
    const upperIdentifier = rawIdentifier.toUpperCase();
    setItemVisibilityFilters(prev => ({
      ...prev,
      [upperIdentifier]: !prev[upperIdentifier] 
    }));
  };
  
  const handleAddVesselToSearchFromGraph = useCallback((formattedIdentifier: string) => {
    const rawId = extractRawId(formattedIdentifier);
    if (currentSearchIdentifiers.includes(rawId)) { 
      console.log(`Vessel ${rawId} (formatted: ${formattedIdentifier}) is already in search queries.`);
      const existingInputIndex = searchQueries.findIndex(q => extractRawId(q) === rawId);
      if (existingInputIndex !== -1) {
         const inputElement = document.getElementById(`search-input-${existingInputIndex}`);
         inputElement?.focus();
      }
      return;
    }

    const emptyInputIndex = searchQueries.findIndex(q => q.trim() === '');
    if (emptyInputIndex !== -1) {
      const newQueries = [...searchQueries];
      newQueries[emptyInputIndex] = formattedIdentifier; 
      setSearchQueries(newQueries);
      handleIndividualSearch(formattedIdentifier); 
    } else {
      setSearchQueries(prev => [...prev, formattedIdentifier]);
      handleIndividualSearch(formattedIdentifier);
    }
  }, [searchQueries, currentSearchIdentifiers]); 

  const handleRemoveVesselFromSearchFromGraph = useCallback((rawIdentifier: string) => {
    const queryToRemove = searchQueries.find(q => extractRawId(q) === rawIdentifier.toUpperCase());
    if (queryToRemove) {
      removeSearchInput(queryToRemove);
    } else {
      removeSearchInput(rawIdentifier); 
      console.warn(`Attempted to remove ${rawIdentifier} from graph, but corresponding query string not found. Cleaned up data by raw ID.`);
    }
  }, [removeSearchInput, searchQueries]);

  const handleToggleVesselVisibilityFromGraph = useCallback((rawIdentifier: string) => {
    toggleItemVisibility(rawIdentifier); 
  }, []);

  const handleShowVesselDetailsFromGraph = useCallback((rawIdentifier: string) => {
    const reportToShow = geminiVesselInfo?.reports.find(r => r.identifier.toUpperCase() === rawIdentifier.toUpperCase());
    if (reportToShow) {
      openModalWithVessel(reportToShow);
    } else {
      console.warn(`Report for vessel ${rawIdentifier} not found for modal display.`);
    }
  }, [geminiVesselInfo?.reports]);

  // Chat Panel Logic
  const toggleChatPanel = () => {
    setIsChatOpen(prev => !prev);
    if (!isChatOpen && !chatSession) { 
        handleRefreshContextAndClearChat(false); 
    }
  };

  const handleRefreshContextAndClearChat = useCallback(async (clearMsgs = true) => {
    setIsChatLoading(true);
    setChatError(null);
    if (clearMsgs) {
        setChatMessages([]);
    }
    try {
        const ai = getAiClient();
        const reportSummary = summarizeVesselReportsForChat(geminiVesselInfo?.reports);
        const graphSummary = geminiVesselInfo?.graphData?.nodes?.length 
            ? `The graph currently has ${geminiVesselInfo.graphData.nodes.length} entities and ${geminiVesselInfo.graphData.edges.length} relationships. Details may be found in the vessel reports.`
            : "No specific graph data is currently loaded.";

        const systemInstruction = `You are an AI Maritime Analyst for PROJECT HELMSMAN. Your role is to answer questions based *only* on the provided context about maritime vessels, their associated entities, and any related graph data summaries. Do not use external knowledge or make assumptions beyond this data. If the information to answer a question is not present in the provided context, clearly state that the information is not available in the current dataset. Keep your answers concise and directly relevant to the question.
        
Context Data:
---BEGIN VESSEL REPORTS SUMMARY---
${reportSummary}
---END VESSEL REPORTS SUMMARY---

---BEGIN GRAPH OVERVIEW---
${graphSummary}
---END GRAPH OVERVIEW---`;

        const newChat = ai.chats.create({
            model: 'gemini-2.5-flash',
            config: { systemInstruction },
        });
        setChatSession(newChat);
        if (clearMsgs || chatMessages.length === 0) { 
            setChatMessages([{ id: Date.now().toString(), sender: 'ai', text: "Chat session initialized for PROJECT HELMSMAN. How can I help you analyze the current vessel data?", timestamp: Date.now() }]);
        }
    } catch (e: any) {
        console.error("Error initializing chat session:", e);
        setChatError(e.message || "Failed to initialize chat session.");
        setChatMessages(prev => [...prev, {id: Date.now().toString(), sender: 'ai', text: `Error: ${e.message || "Failed to initialize chat."}`, timestamp: Date.now()}]);
    } finally {
        setIsChatLoading(false);
    }
  }, [geminiVesselInfo, chatMessages.length]);


  const handleSendChatMessage = async (userMessageText: string) => {
    if (!userMessageText.trim()) return;

    const newUserMessage = { id: Date.now().toString(), sender: 'user' as 'user', text: userMessageText, timestamp: Date.now() };
    setChatMessages(prev => [...prev, newUserMessage]);
    setIsChatLoading(true);
    setChatError(null);

    let currentChat = chatSession;
    if (!currentChat) {
        console.warn("Chat session was null when sending message. Attempting to initialize.");
        try {
            const ai = getAiClient();
            const reportSummary = summarizeVesselReportsForChat(geminiVesselInfo?.reports);
            const graphSummary = geminiVesselInfo?.graphData?.nodes?.length 
                ? `The graph currently has ${geminiVesselInfo.graphData.nodes.length} entities and ${geminiVesselInfo.graphData.edges.length} relationships.`
                : "No specific graph data is currently loaded.";
            const systemInstruction = `You are an AI Maritime Analyst for PROJECT HELMSMAN... (Context as in handleRefreshContextAndClearChat)
            Context Data:
            ---BEGIN VESSEL REPORTS SUMMARY---
            ${reportSummary}
            ---END VESSEL REPORTS SUMMARY---
            ---BEGIN GRAPH OVERVIEW---
            ${graphSummary}
            ---END GRAPH OVERVIEW---`;
            currentChat = ai.chats.create({ model: 'gemini-2.5-flash', config: { systemInstruction } });
            setChatSession(currentChat);
        } catch (e: any) {
            console.error("Error re-initializing chat session:", e);
            setChatError(e.message || "Failed to re-initialize chat session.");
            setChatMessages(prev => [...prev, {id: (Date.now()+1).toString(), sender: 'ai', text: `Error: ${e.message || "Failed to contact AI."}`, timestamp: Date.now()}]);
            setIsChatLoading(false);
            return;
        }
    }
    
    try {
        const response = await currentChat.sendMessage({ message: userMessageText });
        const aiResponseText = response.text;
        setChatMessages(prev => [...prev, { id: (Date.now() + 1).toString(), sender: 'ai', text: aiResponseText, timestamp: Date.now() }]);
    } catch (error: any) {
        console.error("Error sending chat message to Gemini:", error);
        const errText = error.message || "An error occurred while communicating with the AI.";
        setChatError(errText);
        setChatMessages(prev => [...prev, { id: (Date.now() + 1).toString(), sender: 'ai', text: `Error: ${errText}`, timestamp: Date.now() }]);
    } finally {
        setIsChatLoading(false);
    }
  };


  const activeLoaders = Object.values(loadingStates).filter(Boolean).length;
  const showInitiatePlaceholder = !geminiVesselInfo && activeLoaders === 0 && !geminiError;
  
  const visibleReportsForReportView = useMemo(() => {
    if (viewMode !== 'report' || !geminiVesselInfo) return [];
    return geminiVesselInfo.reports.filter(report => itemVisibilityFilters[report.identifier.toUpperCase()] !== false);
  }, [geminiVesselInfo, itemVisibilityFilters, viewMode]);

  const showNoReportsMessage = viewMode === 'report' && geminiVesselInfo && visibleReportsForReportView.length === 0 && geminiVesselInfo.reports.length > 0 && activeLoaders === 0 && !geminiError;
  const showNoReportsYetMessage = viewMode === 'report' && (!geminiVesselInfo || geminiVesselInfo.reports.length === 0) && activeLoaders === 0 && !geminiError;

  const showNoGraphMessage = viewMode === 'graph' && (!geminiVesselInfo || !geminiVesselInfo.graphData || geminiVesselInfo.graphData.nodes.length === 0) && activeLoaders === 0 && !geminiError;
  
  const plottableVesselsOnMap = useMemo(() => { // This is used for the "no map data" message, not to control ArcGisMapDisplay rendering directly.
    if (!geminiVesselInfo?.reports) return [];
    return geminiVesselInfo.reports.filter(r => 
        itemVisibilityFilters[r.identifier.toUpperCase()] !== false &&
        typeof r.latitude === 'number' && typeof r.longitude === 'number'
    );
  }, [geminiVesselInfo, itemVisibilityFilters]);

  const showNoMapDataMessage = viewMode === 'map' && geminiVesselInfo && plottableVesselsOnMap.length === 0 && activeLoaders === 0 && !geminiError;
  
  const quadViewSelectedReport = useMemo(() => {
    if (!quadViewSelectedVesselIdentifier || !geminiVesselInfo || !geminiVesselInfo.reports) return null;
    return geminiVesselInfo.reports.find(r => r.identifier === quadViewSelectedVesselIdentifier) || null;
  }, [quadViewSelectedVesselIdentifier, geminiVesselInfo]);

  const showNoQuadDataMessage = viewMode === 'quad' && !quadViewSelectedReport && activeLoaders === 0 && !geminiError;


  const isSearchAllDisabled = useMemo(() => {
    return !searchQueries.some(q => {
        const trimmedQuery = q.trim();
        const rawId = extractRawId(trimmedQuery);
        return trimmedQuery && rawId.match(/^\d{7,9}$/) && !loadingStates[trimmedQuery];
    });
  }, [searchQueries, loadingStates]);

  const handleQuadVesselSelection = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedIdentifier = event.target.value;
    if (selectedIdentifier && geminiVesselInfo?.reports) {
        const reportExists = geminiVesselInfo.reports.some(r => r.identifier === selectedIdentifier);
        if (reportExists) {
            setQuadViewSelectedVesselIdentifier(selectedIdentifier);
        }
    } else if (!selectedIdentifier) {
         setQuadViewSelectedVesselIdentifier(null);
    }
  };



  return (
    <ReactFlowProvider>
      <div className="flex flex-col items-center p-4 custom-scrollbar min-h-screen">
        <header className="w-full max-w-7xl mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-2">
             <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" className="mr-3 flex-shrink-0" aria-hidden="true">
                    <title>Project Helmsman Logo</title>
                    <circle cx="12" cy="12" r="10" stroke="var(--ds-primary-color)" strokeWidth="1.5"/>
                    <circle cx="12" cy="12" r="2.5" fill="var(--ds-primary-color)" stroke="var(--ds-background-default)" strokeWidth="0.5"/>
                    <path d="M12 9.5V2.5" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M12 14.5V21.5" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M9.5 12H2.5" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M14.5 12H21.5" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M14.475 9.525L18.475 5.525" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/> 
                    <path d="M9.525 9.525L5.525 5.525" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>   
                    <path d="M9.525 14.475L5.525 18.475" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/>  
                    <path d="M14.475 14.475L18.475 18.475" stroke="var(--ds-primary-color)" strokeWidth="1.5" strokeLinecap="round"/> 
                </svg>
                <h1 className="text-3xl sm:text-4xl font-bold mb-2 sm:mb-0" style={{color: 'var(--ds-primary-color)'}}>
                PROJECT HELMSMAN
                </h1>
                <button 
                    onClick={toggleChatPanel}
                    className={`ds-btn ds-btn-icon ml-3 sm:ml-4 ${isChatOpen ? 'ds-btn-primary ds-btn-selected' : 'ds-btn-secondary'}`}
                    title="Toggle AI Analyst Chat"
                    aria-label="Toggle AI Analyst Chat"
                    aria-expanded={isChatOpen}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" fill="none" strokeLinecap="round" strokeLinejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10"></path>
                        <path d="M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2"></path>
                    </svg>
                </button>
            </div>
            <div className="flex space-x-1 sm:space-x-2 mt-2 sm:mt-0">
                {['report', 'graph', 'map', 'quad'].map((mode) => (
                    <button
                        key={mode}
                        onClick={() => setViewMode(mode as ViewMode)}
                        className={`ds-btn ds-btn-small ${viewMode === mode ? 'ds-btn-filled ds-btn-primary' : 'ds-btn-outlined ds-btn-secondary'}`}
                        disabled={viewMode === mode}
                        aria-pressed={viewMode === mode}
                    >
                        {mode.charAt(0).toUpperCase() + mode.slice(1)}
                        {mode === 'quad' && ' Slide'} 
                    </button>
                ))}
            </div>

          </div>
          <p className="text-sm text-center sm:text-left" style={{color: 'var(--ds-text-secondary)'}}>
            Enter IMO (7 digits) or MMSI (9 digits), optionally prefixed with "IMO". Supports multiple searches via paste (comma, space, newline, or semicolon separated).
          </p>
        </header>

        <main className="w-full max-w-7xl flex-grow flex flex-col">
          {viewMode !== 'quad' && (
            <div className="ds-panel p-4 sm:p-6 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                {searchQueries.map((query, index) => {
                  const rawQueryId = extractRawId(query);
                  const isLoading = loadingStates[query.trim()]; 
                  const isVisible = itemVisibilityFilters[rawQueryId.toUpperCase()] !== false; 
                  const isSearched = geminiVesselInfo?.reports.some(r => r.identifier.toUpperCase() === rawQueryId.toUpperCase());
                  
                  return (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      id={`search-input-${index}`}
                      type="text"
                      value={query}
                      onChange={(e) => handleSearchInputChange(index, e.target.value)}
                      placeholder="IMO XXXXXXX / XXXXXXX"
                      className="ds-input flex-grow"
                      aria-label={`Search query ${index + 1}`}
                    />
                    <button
                      onClick={() => handleIndividualSearch(query)}
                      className="ds-btn ds-btn-filled ds-btn-primary" 
                      style={{ padding: '0.625rem 0.75rem' }} // Match input height
                      disabled={!query.trim() || !extractRawId(query).match(/^\d{7,9}$/) || isLoading}
                      aria-label={`Search for ${query || 'query ' + (index+1)}`}
                    >
                      {isLoading ? (
                          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{color: 'var(--ds-primary-text-on-fill)'}}>
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" style={{color: 'var(--ds-primary-text-on-fill)'}}>
                          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                    {isSearched && rawQueryId && ( 
                       <button 
                          onClick={() => toggleItemVisibility(rawQueryId)} 
                          className={`ds-btn ds-btn-icon ${isVisible ? 'ds-btn-primary' : 'ds-btn-secondary'}`}
                          title={isVisible ? `Hide ${query.trim()} data` : `Show ${query.trim()} data`}
                          aria-label={isVisible ? `Hide ${query.trim()} data` : `Show ${query.trim()} data`}
                          aria-pressed={isVisible}
                        >
                         {isVisible ? (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.022 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                          </svg>
                         ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                             <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
                          </svg>
                         )}
                      </button>
                    )}
                    {(searchQueries.length > 1 || query.trim() !== '') && (
                      <button 
                          onClick={() => removeSearchInput(query.trim(), index)} 
                          className="ds-btn ds-btn-icon ds-btn-destructive"
                          title={`Remove search for ${query || 'this input'}`}
                          aria-label={`Remove search query ${index + 1}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg"viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    )}
                  </div>
                )})}
              </div>
              <div className="flex flex-col sm:flex-row justify-start items-center space-y-2 sm:space-y-0 sm:space-x-2">
                  <button onClick={addSearchInput} className="ds-btn ds-btn-outlined ds-btn-secondary w-full sm:w-auto">
                  Add Vessel Search
                  </button>
                  <button 
                      onClick={handleSearchAll} 
                      className="ds-btn ds-btn-filled ds-btn-primary w-full sm:w-auto"
                      disabled={isSearchAllDisabled}
                  >
                      Search All
                  </button>
                  <div className="flex-grow"></div> 
                  <button onClick={clearGeminiSearch} className="ds-btn ds-btn-outlined ds-btn-destructive w-full sm:w-auto"
                          disabled={!geminiVesselInfo && activeLoaders === 0}
                  >
                  Clear All Results
                  </button>
              </div>
            </div>
          )}
          
          {viewMode === 'quad' && geminiVesselInfo && geminiVesselInfo.reports.length > 0 && (
            <div className="ds-panel p-4 sm:p-6 mb-6">
                <label htmlFor="quad-vessel-select" className="block text-sm font-medium mb-1" style={{color: 'var(--ds-text-secondary)'}}>Select Vessel for Quad View:</label>
                <select 
                    id="quad-vessel-select"
                    value={quadViewSelectedVesselIdentifier || ''}
                    onChange={handleQuadVesselSelection}
                    className="ds-input w-full sm:w-auto"
                    aria-label="Select vessel for Quad Slide view"
                >
                    {!quadViewSelectedVesselIdentifier && geminiVesselInfo.reports.length > 0 && (
                         <option value="" disabled>Select a vessel...</option>
                    )}
                    {geminiVesselInfo.reports.map(report => (
                        <option key={report.identifier} value={report.identifier}>
                            {report.vesselName || `IMO ${report.identifier}`} ({report.identifier})
                        </option>
                    ))}
                </select>
            </div>
          )}
           {viewMode === 'quad' && (!geminiVesselInfo || geminiVesselInfo.reports.length === 0) && !showInitiatePlaceholder && (
                <div className="ds-panel p-4 sm:p-6 mb-6 text-center" style={{color: 'var(--ds-text-secondary)'}}>
                    No vessel reports available to select for Quad View. Please perform a search first.
                </div>
            )}


          {geminiError && (
            <div className="px-4 py-3 rounded-lg relative mb-4 shadow-lg" role="alert" style={{backgroundColor: 'var(--ds-destructive-color)', color: 'var(--ds-destructive-text-on-fill)', border: '1px solid var(--ds-destructive-hover-color)'}}>
              <strong className="font-bold">Error:</strong>
              <span className="block sm:inline ml-2">{geminiError}</span>
            </div>
          )}

          {showInitiatePlaceholder && ( 
            <div className="text-center py-10" style={{color: 'var(--ds-text-secondary)'}}>
              <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--ds-secondary-color)'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
              </svg>
              Enter an IMO/MMSI and click search to initiate a vessel inquiry.
            </div>
          )}

          {viewMode === 'report' && (
             <>
              {showNoReportsMessage && !showInitiatePlaceholder && (
                <div className="text-center py-10" style={{color: 'var(--ds-text-secondary)'}}>
                     <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{color: 'var(--ds-secondary-color)'}}><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                     All reports are currently hidden or no reports match the current filter. Adjust visibility using the eye icons.
                </div>
              )}
              {showNoReportsYetMessage && !showInitiatePlaceholder && (
                 <div className="text-center py-10" style={{color: 'var(--ds-text-secondary)'}}>
                     <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{color: 'var(--ds-secondary-color)'}}><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                     No reports generated yet, or all reports have been cleared.
                </div>
              )}
              {geminiVesselInfo && visibleReportsForReportView.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                  {visibleReportsForReportView.map((report) => (
                    <VesselInfoCard 
                        key={report.identifier} 
                        report={report} 
                        onClick={() => handleCardClick(report)}
                    />
                  ))}
                </div>
              )}
             </>
          )}

          {viewMode === 'graph' && (
            <>
            {geminiVesselInfo && geminiVesselInfo.graphData && geminiVesselInfo.graphData.nodes.length > 0 && !showInitiatePlaceholder &&(
                <NetworkGraphDisplay 
                    graphData={geminiVesselInfo.graphData} 
                    visibilityFilters={itemVisibilityFilters} 
                    currentSearchIdentifiers={currentSearchIdentifiers} 
                    onAddVesselToSearch={handleAddVesselToSearchFromGraph}
                    onRemoveVesselFromSearch={handleRemoveVesselFromSearchFromGraph}
                    onToggleVesselVisibility={handleToggleVesselVisibilityFromGraph}
                    onShowVesselDetails={handleShowVesselDetailsFromGraph}
                />
            )}
            {showNoGraphMessage && !showInitiatePlaceholder && (
                 <div className="text-center py-10" style={{color: 'var(--ds-text-secondary)'}}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{color: 'var(--ds-secondary-color)'}}><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                     No graph data available, or all graph data has been cleared/filtered out.
                </div>
            )}
            </>
          )}

          {viewMode === 'map' && (
            <>
              {/* Render ArcGisMapDisplay if geminiVesselInfo exists, not dependent on plottableVesselsOnMap.length > 0 */}
              {geminiVesselInfo && !showInitiatePlaceholder && (
                <div 
                  style={{ height: 'calc(100vh - 360px)', width: '100%', backgroundColor: 'var(--ds-background-component)', border: '1px solid var(--ds-border-default)' }} 
                  className="rounded-lg relative shadow-xl overflow-hidden"
                  aria-label="Map container for vessel locations"
                >
                  <ArcGisMapDisplay 
                    vesselReports={geminiVesselInfo.reports} 
                    itemVisibilityFilters={itemVisibilityFilters}
                  />
                </div>
              )}
              {/* This message is shown if no vessels are plottable, ArcGisMapDisplay still renders the basemap etc. */}
              {showNoMapDataMessage && !showInitiatePlaceholder &&(
                 <div className="text-center py-10" style={{color: 'var(--ds-text-secondary)'}}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--ds-secondary-color)'}}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    No vessels with location data to display on the map, or all are hidden.
                </div>
              )}
            </>
          )}

          {viewMode === 'quad' && (
            <>
              {quadViewSelectedReport && geminiVesselInfo && !showInitiatePlaceholder ? (
                  <QuadSlideDisplay
                    selectedReport={quadViewSelectedReport} // Pass the individual selected report
                    allReports={geminiVesselInfo.reports} // Pass all reports for context if needed by children
                    fullGraphData={geminiVesselInfo.graphData} 
                    currentSearchIdentifiers={currentSearchIdentifiers}
                    onAddVesselToSearch={handleAddVesselToSearchFromGraph}
                    onRemoveVesselFromSearch={handleRemoveVesselFromSearchFromGraph}
                    onToggleVesselVisibility={handleToggleVesselVisibilityFromGraph}
                    onShowVesselDetails={handleShowVesselDetailsFromGraph}
                    visibilityFilters={itemVisibilityFilters} // Pass the global visibility filters
                  />
              ) : (
                showNoQuadDataMessage && !showInitiatePlaceholder && (
                    <div className="text-center py-10 flex-grow flex flex-col justify-center items-center" style={{color: 'var(--ds-text-secondary)'}}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1" style={{color: 'var(--ds-secondary-color)'}}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        No vessel selected for Quad Slide view, or no reports available. Please search for a vessel and select it using the dropdown above.
                    </div>
                 )
              )}
            </>
          )}
          
          {isModalOpen && selectedVesselForModal && (
            <Modal 
                isOpen={isModalOpen} 
                onClose={closeModal} 
                title={`OSINT Report: ${selectedVesselForModal.vesselName || 'Vessel'} - IMO ${selectedVesselForModal.identifier}`}
            >
              <GeminiVesselInfoDisplay report={selectedVesselForModal} />
            </Modal>
          )}

        </main>
        <footer className="w-full max-w-7xl mt-8 py-4 border-t text-center" style={{borderColor: 'var(--ds-border-default)'}}>
            <p className="text-sm" style={{color: 'var(--ds-text-secondary)'}}>
            PROJECT HELMSMAN. AI-powered insights using Gemini.
            </p>
        </footer>
      </div>
      
      <ChatInterface
        isOpen={isChatOpen}
        onClose={toggleChatPanel}
        messages={chatMessages}
        onSendMessage={handleSendChatMessage}
        isLoading={isChatLoading}
        error={chatError}
        onRefreshContext={handleRefreshContextAndClearChat}
      />
    </ReactFlowProvider>
  );
};

export default App;