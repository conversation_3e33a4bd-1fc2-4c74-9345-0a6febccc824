
import React from 'react';
import { VesselReport } from '../types';

interface QuadSocialIntelPanelProps {
  report: VesselReport;
}

const QuadSocialIntelPanel: React.FC<QuadSocialIntelPanelProps> = ({ report }) => {

  if (!report.crowlingoData || report.crowlingoData.mentions.length === 0) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <p className="text-sm" style={{ color: 'var(--ds-text-secondary)' }}>
          No social intelligence data available for this vessel.
        </p>
      </div>
    );
  }

  const getSentimentIcon = (sentiment: 'positive' | 'negative' | 'neutral') => {
    switch (sentiment) {
      case 'positive':
        return (
          <div className="flex items-center text-green-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-semibold">Positive</span>
          </div>
        );
      case 'negative':
        return (
          <div className="flex items-center text-red-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
             <span className="font-semibold">Negative</span>
          </div>
        );
      default: // neutral
        return (
          <div className="flex items-center text-yellow-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
             <span className="font-semibold">Neutral</span>
          </div>
        );
    }
  };

  return (
    <div className="p-3 h-full custom-scrollbar overflow-y-auto">
      <ul className="space-y-3">
        {report.crowlingoData.mentions.map((mention, index) => (
          <li key={index} className="p-3 rounded-md" style={{ backgroundColor: 'var(--ds-border-interactive)' }}>
            <p className="text-sm mb-2" style={{ color: 'var(--ds-text-primary)' }}>
              "{mention.content}"
            </p>
            <div className="flex flex-col sm:flex-row justify-between sm:items-center text-xs" style={{ color: 'var(--ds-text-secondary)' }}>
                <div className="mb-1 sm:mb-0">
                    <span>Source: <strong>{mention.source}</strong></span>
                    {mention.user && <span className="ml-2">by {mention.user}</span>}
                </div>
                {getSentimentIcon(mention.sentiment)}
            </div>
             <div className="text-xs mt-1" style={{ color: 'var(--ds-text-secondary)' }}>
                {new Date(mention.timestamp).toLocaleString()}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default QuadSocialIntelPanel;
