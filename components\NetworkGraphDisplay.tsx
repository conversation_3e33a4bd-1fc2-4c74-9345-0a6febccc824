

import React, { useMemo, useCallback, useEffect } from 'react';
import React<PERSON>low, {
  Controls,
  Background,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  Position,
  MarkerType,
  useReactFlow,
  NodeProps, 
  BackgroundVariant, 
} from 'reactflow';
import dagre from '@dagrejs/dagre';
import { GraphData, GraphNode as CustomGraphNodeTypeFromTypes, VesselReport } from '../types';
import CustomGraphNode from './CustomGraphNode'; 
import { getNodeColor, NODE_WIDTH, NODE_HEIGHT } from './utils/graphUtils'; 

export interface CustomNodeDataForContextMenu extends CustomGraphNodeTypeFromTypes {
    identifier: string;
    currentSearchIdentifiers: string[];
    itemVisibilityFilters: Record<string, boolean>; // Updated prop name
    onAddVesselToSearch: (identifier: string) => void;
    onRemoveVesselFromSearch: (identifier: string) => void;
    onToggleVesselVisibility: (identifier: string) => void;
    onShowVesselDetails: (identifier: string) => void;
}


interface NetworkGraphDisplayProps {
  graphData: GraphData;
  visibilityFilters: Record<string, boolean>; // This is itemVisibilityFilters from App.tsx
  currentSearchIdentifiers: string[];
  onAddVesselToSearch: (identifier: string) => void;
  onRemoveVesselFromSearch: (identifier: string) => void;
  onToggleVesselVisibility: (identifier: string) => void;
  onShowVesselDetails: (identifier: string) => void;
}

const dagreGraph = new dagre.graphlib.Graph();
dagreGraph.setDefaultEdgeLabel(() => ({}));


const NetworkGraphDisplay: React.FC<NetworkGraphDisplayProps> = ({ 
    graphData, 
    visibilityFilters, // This prop receives itemVisibilityFilters
    currentSearchIdentifiers,
    onAddVesselToSearch,
    onRemoveVesselFromSearch,
    onToggleVesselVisibility,
    onShowVesselDetails
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const { fitView, getNodes: getReactFlowNodes, getEdges: getReactFlowEdges } = useReactFlow();

  const onLayout = useCallback(
    (direction: 'TB' | 'LR') => {
      const currentNodesForLayout = getReactFlowNodes(); 
      const currentEdgesForLayout = getReactFlowEdges(); 

      if (currentNodesForLayout.length === 0) return;

      dagreGraph.setGraph({ rankdir: direction, nodesep: 70, ranksep: 90 });

      currentNodesForLayout.forEach((node) => {
        dagreGraph.setNode(node.id, { width: NODE_WIDTH, height: NODE_HEIGHT });
      });

      currentEdgesForLayout.forEach((edge) => {
        dagreGraph.setEdge(edge.source, edge.target);
      });

      dagre.layout(dagreGraph);

      setNodes((nds) =>
        nds.map((node) => {
          const nodeWithPosition = dagreGraph.node(node.id);
          const xPos = nodeWithPosition ? nodeWithPosition.x - NODE_WIDTH / 2 : node.position.x;
          const yPos = nodeWithPosition ? nodeWithPosition.y - NODE_HEIGHT / 2 : node.position.y;
          return {
            ...node,
            targetPosition: direction === 'TB' ? Position.Top : Position.Left,
            sourcePosition: direction === 'TB' ? Position.Bottom : Position.Right,
            position: {
              x: xPos,
              y: yPos,
            },
          };
        })
      );
      
      setTimeout(() => {
        fitView({ duration: 800, padding: 0.1 });
      }, 100); 
    },
    [getReactFlowNodes, getReactFlowEdges, setNodes, fitView] 
  );
  
  const nodeTypes = useMemo(() => ({
      vessel: (props: NodeProps<CustomNodeDataForContextMenu['data']>) => (
        <CustomGraphNode {...props} />
      ),
      company: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
      person: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
      sanction: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
      location: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
      document: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
      other: (props: NodeProps<CustomGraphNodeTypeFromTypes['data']>) => (
        <CustomGraphNode {...props} />
      ),
  }), []);


  useEffect(() => {
    if (graphData && graphData.nodes) {
      const allOriginalNodes = graphData.nodes;

      const filteredNodes = allOriginalNodes.filter(node => {
        if (!node.sourceIdentifiers || node.sourceIdentifiers.length === 0) {
          return true; 
        }
        return node.sourceIdentifiers.some(id => visibilityFilters[id.toUpperCase()] !== false);
      });

      const visibleNodeIds = new Set(filteredNodes.map(n => n.id));

      const initialNodes: Node<CustomGraphNodeTypeFromTypes['data'] | CustomNodeDataForContextMenu['data']>[] = filteredNodes.map((node, index) => {
        const nodeSemanticType = node.type || 'other';
        
        let enhancedData: CustomGraphNodeTypeFromTypes['data'] | CustomNodeDataForContextMenu['data'] = {
            label: node.label,
            type: nodeSemanticType,
            sourceIdentifiers: node.sourceIdentifiers,
            ...(node.data || {}) 
        };

        if (nodeSemanticType === 'vessel') {
            enhancedData = {
                ...enhancedData,
                identifier: node.id, 
                currentSearchIdentifiers: currentSearchIdentifiers,
                itemVisibilityFilters: visibilityFilters, // Pass down itemVisibilityFilters
                onAddVesselToSearch: onAddVesselToSearch,
                onRemoveVesselFromSearch: onRemoveVesselFromSearch,
                onToggleVesselVisibility: onToggleVesselVisibility,
                onShowVesselDetails: onShowVesselDetails,
            };
        }

        return {
          id: String(node.id),
          type: nodeSemanticType, 
          data: enhancedData,
          position: node.position || { x: (index % 10) * (NODE_WIDTH + 70) , y: Math.floor(index / 10) * (NODE_HEIGHT + 90) },
        };
      });
      setNodes(initialNodes);

      if (graphData.edges) {
        const filteredEdges: Edge[] = graphData.edges.filter(edge => 
          visibleNodeIds.has(String(edge.source)) && visibleNodeIds.has(String(edge.target))
        ).map(edge => ({
          id: String(edge.id),
          source: String(edge.source),
          target: String(edge.target),
          label: edge.label,
          type: edge.type || 'smoothstep', 
          animated: edge.animated !== undefined ? edge.animated : true,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 15,
            height: 15,
            color: 'var(--ds-primary-color)', 
          },
          style: {
              stroke: 'var(--ds-primary-color)', 
              strokeWidth: 1.5,
          },
          labelStyle: { fill: 'var(--ds-text-secondary)', fontSize: 10, fontWeight: 500 },
          labelBgStyle: { fill: 'var(--ds-background-default)', fillOpacity: 0.7 }, 
          labelBgPadding: [4, 2],
          labelBgBorderRadius: 2,
        }));
        setEdges(filteredEdges);
      } else {
        setEdges([]);
      }
      
      if (initialNodes.length > 0) {
        setTimeout(() => onLayout('TB'), 150);
      }

    } else {
        setNodes([]);
        setEdges([]);
    }
  }, [graphData, visibilityFilters, setNodes, setEdges, onLayout, 
      currentSearchIdentifiers, onAddVesselToSearch, onRemoveVesselFromSearch, 
      onToggleVesselVisibility, onShowVesselDetails]); 

  const onConnect = useCallback(
    (params: Connection | Edge) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  if (!graphData || !graphData.nodes || graphData.nodes.length === 0) {
    return <div className="p-6 text-center" style={{color: 'var(--ds-text-secondary)'}}>No graph data available to display.</div>;
  }
  
  if (nodes.length === 0 && graphData.nodes.length > 0) {
    return (
      <div className="p-6 text-center" style={{color: 'var(--ds-text-secondary)'}}>
        All graph elements are currently hidden. Adjust visibility filters using the eye icons next to search inputs.
      </div>
    );
  }

  return (
    <div style={{ height: 'calc(100vh - 290px)', width: '100%', backgroundColor: 'var(--ds-background-component)' }} className="rounded-lg relative">
      <div className="absolute top-4 right-4 z-10 space-x-2">
        <button onClick={() => onLayout('TB')} className="ds-btn ds-btn-outlined ds-btn-secondary ds-btn-small">
          Layout TB
        </button>
        <button onClick={() => onLayout('LR')} className="ds-btn ds-btn-outlined ds-btn-secondary ds-btn-small">
          Layout LR
        </button>
      </div>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
        attributionPosition="bottom-left"
        nodeTypes={nodeTypes} 
      >
        <Controls />
        <MiniMap 
          nodeColor={(node: Node<CustomGraphNodeTypeFromTypes['data']>) => getNodeColor(node.data?.type).background}
          nodeStrokeColor={(node: Node<CustomGraphNodeTypeFromTypes['data']>) => getNodeColor(node.data?.type).border}
          nodeBorderRadius={2}
          style={{ backgroundColor: 'var(--ds-background-default)', borderColor: 'var(--ds-border-default)' }}
          maskColor="rgba(31, 41, 55, 0.6)"
        />
        <Background gap={16} color="var(--ds-border-interactive)" variant={BackgroundVariant.Dots} />
      </ReactFlow>
    </div>
  );
};

export default NetworkGraphDisplay;