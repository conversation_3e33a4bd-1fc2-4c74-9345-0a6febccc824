
import React, { useState, useEffect } from 'react';
import { VesselReport } from '../types';
import { generateVesselAISummary, AISummary } from '../services/geminiService';

interface QuadVesselAISummaryPanelProps {
  report: VesselReport;
}

const QuadVesselAISummaryPanel: React.FC<QuadVesselAISummaryPanelProps> = ({ report }) => {
  const [summary, setSummary] = useState<AISummary | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!report) {
      setSummary(null);
      setError(null);
      setIsLoading(false);
      return;
    }

    const fetchSummary = async () => {
      setIsLoading(true);
      setError(null);
      setSummary(null); 
      try {
        const result = await generateVesselAISummary(report);
        setSummary(result);
      } catch (e: any) {
        console.error("Error fetching AI summary for Quad Panel:", e);
        setError(e.message || 'Failed to load AI summary.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [report]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <div className="text-center">
            <svg className="animate-spin h-6 w-6 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{color: 'var(--ds-primary-color)'}}>
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-sm" style={{color: 'var(--ds-text-secondary)'}}>Generating AI Summary...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <p className="text-sm text-center" style={{color: 'var(--ds-destructive-color)'}}>
          Error: {error}
        </p>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <p className="text-sm" style={{color: 'var(--ds-text-secondary)'}}>No AI summary available.</p>
      </div>
    );
  }

  const BulletPoint: React.FC<{ icon: React.ReactNode; text: string; label: string }> = ({ icon, text, label }) => (
    <li className="flex items-start space-x-3 py-2">
        <span className="flex-shrink-0 w-5 h-5 mt-0.5" style={{ color: 'var(--ds-primary-color)'}} aria-hidden="true">{icon}</span>
        <div>
            <span className="block text-xs font-semibold" style={{color: 'var(--ds-text-secondary)'}}>{label}:</span>
            <span className="block text-sm leading-snug" style={{color: 'var(--ds-text-primary)'}}>{text}</span>
        </div>
    </li>
  );

  return (
    <div className="p-4 h-full custom-scrollbar overflow-y-auto">
      <ul className="space-y-2">
        <BulletPoint 
            label="AI Analyst Thoughts"
            text={summary.analystThoughts || "No specific thoughts provided."} 
            icon={
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-7.5h12.75" />
                </svg>
            }
        />
        <BulletPoint 
            label="Likelihood of Risk"
            text={summary.riskLikelihoodSummary || "Risk assessment not available."}
            icon={
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                     <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m0-10.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.249-8.25-3.286zm0 13.036h.008v.008H12v-.008z" />
                </svg>
            }
        />
        <BulletPoint 
            label="Other Points of Interest"
            text={summary.otherPointsOfInterest || "No other specific points highlighted."}
            icon={
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                </svg>
            }
        />
      </ul>
    </div>
  );
};

export default QuadVesselAISummaryPanel;
